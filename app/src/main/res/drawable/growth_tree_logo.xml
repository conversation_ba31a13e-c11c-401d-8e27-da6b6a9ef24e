<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">

    <!-- 背景圆圈 -->
    <path
        android:fillColor="#F8F9FA"
        android:pathData="M60,10 A50,50 0 1,1 60,110 A50,50 0 1,1 60,10 Z" />

    <!-- 装饰星星 -->
    <group android:translateX="20" android:translateY="25">
        <path
            android:fillColor="#FFD700"
            android:pathData="M0,0 L2,6 L8,6 L3,10 L5,16 L0,12 L-5,16 L-3,10 L-8,6 L-2,6 Z" />
    </group>

    <group android:translateX="95" android:translateY="35">
        <path
            android:fillColor="#FFD700"
            android:pathData="M0,0 L1.5,4.5 L6,4.5 L2.25,7.5 L3.75,12 L0,9 L-3.75,12 L-2.25,7.5 L-6,4.5 L-1.5,4.5 Z" />
    </group>

    <group android:translateX="25" android:translateY="80">
        <path
            android:fillColor="#FFD700"
            android:pathData="M0,0 L1,3 L4,3 L1.5,5 L2.5,8 L0,6 L-2.5,8 L-1.5,5 L-4,3 L-1,3 Z" />
    </group>

    <group android:translateX="90" android:translateY="75">
        <path
            android:fillColor="#FFD700"
            android:pathData="M0,0 L1,3 L4,3 L1.5,5 L2.5,8 L0,6 L-2.5,8 L-1.5,5 L-4,3 L-1,3 Z" />
    </group>

    <!-- 装饰彩带 -->
    <path
        android:strokeColor="#FF6B9D"
        android:strokeWidth="2"
        android:pathData="M15,45 Q25,40 35,45 Q45,50 55,45" />

    <path
        android:strokeColor="#4ECDC4"
        android:strokeWidth="2"
        android:pathData="M65,45 Q75,40 85,45 Q95,50 105,45" />

    <path
        android:strokeColor="#45B7D1"
        android:strokeWidth="2"
        android:pathData="M20,85 Q30,80 40,85 Q50,90 60,85" />

    <!-- 树干 -->
    <path
        android:fillColor="#8B4513"
        android:pathData="M55,70 L65,70 L67,95 L53,95 Z" />

    <!-- 树干纹理 -->
    <path
        android:strokeColor="#654321"
        android:strokeWidth="1"
        android:pathData="M57,75 L63,75 M56,80 L64,80 M57,85 L63,85 M56,90 L64,90" />

    <!-- 叶子层级 - 使用path代替ellipse -->

    <!-- 第一层叶子 (最下层) -->
    <path
        android:fillColor="#228B22"
        android:pathData="M52,65 Q60,59 68,65 Q60,71 52,65 Z" />

    <!-- 第二层叶子 -->
    <path
        android:fillColor="#32CD32"
        android:pathData="M45,58 Q52,53 59,58 Q52,63 45,58 Z" />

    <path
        android:fillColor="#32CD32"
        android:pathData="M61,58 Q68,53 75,58 Q68,63 61,58 Z" />

    <!-- 第三层叶子 -->
    <path
        android:fillColor="#90EE90"
        android:pathData="M54,52 Q60,48 66,52 Q60,56 54,52 Z" />

    <!-- 第四层叶子 -->
    <path
        android:fillColor="#98FB98"
        android:pathData="M50,46 Q55,42 60,46 Q55,50 50,46 Z" />

    <path
        android:fillColor="#98FB98"
        android:pathData="M60,46 Q65,42 70,46 Q65,50 60,46 Z" />

    <!-- 第五层叶子 -->
    <path
        android:fillColor="#ADFF2F"
        android:pathData="M56,40 Q60,37 64,40 Q60,43 56,40 Z" />

    <!-- 顶部叶子 -->
    <path
        android:fillColor="#9AFF9A"
        android:pathData="M55,35 Q58,32.5 61,35 Q58,37.5 55,35 Z" />

    <path
        android:fillColor="#9AFF9A"
        android:pathData="M59,35 Q62,32.5 65,35 Q62,37.5 59,35 Z" />

    <!-- 叶子细节和纹理 -->
    <path
        android:strokeColor="#006400"
        android:strokeWidth="0.5"
        android:pathData="M60,65 L60,61 M52,58 L52,54 M68,58 L68,54 M60,52 L60,48 M55,46 L55,42 M65,46 L65,42 M60,40 L60,36" />

    <!-- 小果实装饰 -->
    <path
        android:fillColor="#FF4500"
        android:pathData="M65,50 A1.5,1.5 0 1,1 65,47 A1.5,1.5 0 1,1 65,50 Z" />

    <path
        android:fillColor="#FF6347"
        android:pathData="M55,55 A1.5,1.5 0 1,1 55,52 A1.5,1.5 0 1,1 55,55 Z" />

    <path
        android:fillColor="#FF4500"
        android:pathData="M62,42 A1,1 0 1,1 62,40 A1,1 0 1,1 62,42 Z" />

</vector>
