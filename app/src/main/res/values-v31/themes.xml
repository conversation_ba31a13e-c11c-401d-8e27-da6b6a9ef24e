<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 启动页主题 - Android 12+ (API 31+) -->
    <style name="Theme.Child.Splash" parent="Theme.SplashScreen">
        <!-- 启动画面背景色 -->
        <item name="android:windowSplashScreenBackground">@color/white</item>
        
        <!-- 启动画面图标 -->
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/growth_tree_logo</item>
        
        <!-- 图标背景 -->
        <item name="android:windowSplashScreenIconBackgroundColor">@color/white</item>
        
        <!-- 动画持续时间设为0，立即显示 -->
        <item name="android:windowSplashScreenAnimationDuration">0</item>
        
        <!-- 品牌图像（可选） -->
        <item name="android:windowSplashScreenBrandingImage">@drawable/growth_tree_logo</item>
        
        <!-- 状态栏设置 -->
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>
        
        <!-- 导航栏设置 -->
        <item name="android:navigationBarColor">@color/white</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- 禁用预览 -->
        <item name="android:windowDisablePreview">true</item>
    </style>
</resources>
