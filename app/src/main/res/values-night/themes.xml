<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Child" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <!-- 启动页主题 - 夜间模式 -->
    <style name="Theme.Child.Splash" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- 使用自定义启动背景 -->
        <item name="android:windowBackground">@drawable/splash_background</item>

        <!-- 关键：禁用启动窗口 -->
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <!-- 状态栏设置 -->
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>

        <!-- 强制禁用启动画面 -->
        <item name="android:windowSplashScreenBackground">@color/white</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/growth_tree_logo</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
    </style>
</resources>