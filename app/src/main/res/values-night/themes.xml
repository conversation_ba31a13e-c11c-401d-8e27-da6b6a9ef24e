<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Child" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <!-- 启动页主题 - 夜间模式 -->
    <style name="Theme.Child.Splash" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- 夜间模式也使用白色背景，保持品牌一致性 -->
        <item name="android:windowBackground">@drawable/splash_background</item>

        <!-- 禁用默认启动页相关设置 -->
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>

        <!-- 状态栏和导航栏设置 - 夜间模式 -->
        <item name="android:statusBarColor">@color/white</item>
        <item name="android:navigationBarColor">@color/white</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        <!-- 全屏设置 -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>
</resources>