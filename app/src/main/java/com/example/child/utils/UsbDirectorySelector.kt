package com.example.child.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.util.Log
import androidx.documentfile.provider.DocumentFile
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * USB设备目录选择器
 * 帮助用户选择USB设备目录进行文件访问
 */
@Singleton
class UsbDirectorySelector @Inject constructor(
    @ApplicationContext private val context: Context,
    private val safFileAccessManager: SafFileAccessManager
) {
    
    companion object {
        private const val TAG = "UsbDirectorySelector"
    }
    
    // 检测到的存储卷
    private val _storageVolumes = MutableStateFlow<List<UsbStorageVolume>>(emptyList())
    val storageVolumes: StateFlow<List<UsbStorageVolume>> = _storageVolumes.asStateFlow()
    
    // 选择状态
    private val _selectionState = MutableStateFlow<DirectorySelectionState>(DirectorySelectionState.Idle)
    val selectionState: StateFlow<DirectorySelectionState> = _selectionState.asStateFlow()
    
    /**
     * 检测可用的存储卷
     */
    suspend fun detectStorageVolumes(): List<UsbStorageVolume> {
        return withContext(Dispatchers.IO) {
            try {
                _selectionState.value = DirectorySelectionState.Detecting("正在检测存储设备...")
                
                val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
                val volumes = mutableListOf<UsbStorageVolume>()
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    storageManager.storageVolumes.forEach { volume ->
                        val usbVolume = createUsbStorageVolume(volume)
                        if (usbVolume != null) {
                            volumes.add(usbVolume)
                        }
                    }
                }
                
                _storageVolumes.value = volumes
                _selectionState.value = DirectorySelectionState.Ready("检测到 ${volumes.size} 个存储设备")
                
                Log.d(TAG, "检测到 ${volumes.size} 个存储卷")
                return@withContext volumes
                
            } catch (e: Exception) {
                Log.e(TAG, "检测存储卷失败", e)
                _selectionState.value = DirectorySelectionState.Error("检测存储设备失败: ${e.message}")
                return@withContext emptyList()
            }
        }
    }
    
    /**
     * 创建USB存储卷信息
     */
    private fun createUsbStorageVolume(volume: StorageVolume): UsbStorageVolume? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                UsbStorageVolume(
                    uuid = volume.uuid,
                    description = volume.getDescription(context),
                    isPrimary = volume.isPrimary,
                    isRemovable = volume.isRemovable,
                    isEmulated = volume.isEmulated,
                    state = volume.state,
                    storageVolume = volume
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建存储卷信息失败", e)
            null
        }
    }
    
    /**
     * 为特定存储卷创建目录选择Intent
     */
    fun createVolumeDirectoryPickerIntent(volume: UsbStorageVolume): Intent? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 可以指定特定存储卷
                volume.storageVolume.createOpenDocumentTreeIntent()
            } else {
                // 降级到通用目录选择
                safFileAccessManager.createDirectoryPickerIntent()
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建存储卷目录选择Intent失败", e)
            safFileAccessManager.createDirectoryPickerIntent()
        }
    }
    
    /**
     * 获取推荐的USB存储卷
     */
    fun getRecommendedUsbVolumes(): List<UsbStorageVolume> {
        return _storageVolumes.value.filter { volume ->
            // 推荐可移动的非模拟存储卷
            volume.isRemovable && !volume.isEmulated && !volume.isPrimary
        }
    }
    
    /**
     * 获取所有外部存储卷
     */
    fun getAllExternalVolumes(): List<UsbStorageVolume> {
        return _storageVolumes.value.filter { volume ->
            !volume.isPrimary
        }
    }
    
    /**
     * 验证选择的目录是否为USB设备
     */
    suspend fun validateUsbDirectory(uri: Uri): UsbDirectoryValidation {
        return withContext(Dispatchers.IO) {
            try {
                _selectionState.value = DirectorySelectionState.Validating("正在验证目录...")
                
                val documentFile = DocumentFile.fromTreeUri(context, uri)
                if (documentFile == null || !documentFile.exists()) {
                    return@withContext UsbDirectoryValidation(
                        isValid = false,
                        isUsbDevice = false,
                        message = "无效的目录"
                    )
                }
                
                // 检查是否为USB设备目录
                val isUsbDevice = isUsbDeviceDirectory(uri)
                val volumeInfo = getVolumeInfoForUri(uri)
                
                val validation = UsbDirectoryValidation(
                    isValid = true,
                    isUsbDevice = isUsbDevice,
                    message = if (isUsbDevice) "USB设备目录验证成功" else "目录验证成功（非USB设备）",
                    volumeInfo = volumeInfo
                )
                
                _selectionState.value = DirectorySelectionState.Selected(validation.message, uri)
                return@withContext validation
                
            } catch (e: Exception) {
                Log.e(TAG, "验证目录失败", e)
                val validation = UsbDirectoryValidation(
                    isValid = false,
                    isUsbDevice = false,
                    message = "目录验证失败: ${e.message}"
                )
                _selectionState.value = DirectorySelectionState.Error(validation.message)
                return@withContext validation
            }
        }
    }
    
    /**
     * 检查URI是否指向USB设备
     */
    private fun isUsbDeviceDirectory(uri: Uri): Boolean {
        return try {
            val path = uri.path ?: ""
            // 检查路径是否包含USB相关标识
            path.contains("usb", ignoreCase = true) ||
            path.contains("otg", ignoreCase = true) ||
            path.contains("external", ignoreCase = true) ||
            uri.authority?.contains("com.android.externalstorage") == true
        } catch (e: Exception) {
            Log.e(TAG, "检查USB设备目录失败", e)
            false
        }
    }
    
    /**
     * 获取URI对应的存储卷信息
     */
    private fun getVolumeInfoForUri(uri: Uri): String? {
        return try {
            val matchingVolume = _storageVolumes.value.find { volume ->
                uri.path?.contains(volume.uuid ?: "", ignoreCase = true) == true
            }
            matchingVolume?.description
        } catch (e: Exception) {
            Log.e(TAG, "获取存储卷信息失败", e)
            null
        }
    }
    
    /**
     * 清除选择状态
     */
    fun clearSelection() {
        _selectionState.value = DirectorySelectionState.Idle
    }
    
    /**
     * 获取选择状态描述
     */
    fun getSelectionStateDescription(state: DirectorySelectionState): String {
        return when (state) {
            is DirectorySelectionState.Idle -> "等待检测存储设备"
            is DirectorySelectionState.Detecting -> state.message
            is DirectorySelectionState.Ready -> state.message
            is DirectorySelectionState.Validating -> state.message
            is DirectorySelectionState.Selected -> state.message
            is DirectorySelectionState.Error -> state.message
        }
    }
}

/**
 * USB存储卷信息
 */
data class UsbStorageVolume(
    val uuid: String?,
    val description: String,
    val isPrimary: Boolean,
    val isRemovable: Boolean,
    val isEmulated: Boolean,
    val state: String,
    val storageVolume: StorageVolume
)

/**
 * 目录选择状态
 */
sealed class DirectorySelectionState {
    object Idle : DirectorySelectionState()
    data class Detecting(val message: String) : DirectorySelectionState()
    data class Ready(val message: String) : DirectorySelectionState()
    data class Validating(val message: String) : DirectorySelectionState()
    data class Selected(val message: String, val uri: Uri) : DirectorySelectionState()
    data class Error(val message: String) : DirectorySelectionState()
}

/**
 * USB目录验证结果
 */
data class UsbDirectoryValidation(
    val isValid: Boolean,
    val isUsbDevice: Boolean,
    val message: String,
    val volumeInfo: String? = null
)
