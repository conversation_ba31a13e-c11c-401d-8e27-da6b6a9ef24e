package com.example.child.utils

import android.content.Context
import android.content.pm.PackageManager
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.Build
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * USB兼容性检查工具
 * 检查设备是否支持USB Host功能，以及相关权限状态
 */
@Singleton
class UsbCompatibilityChecker @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "UsbCompatibilityChecker"
    }
    
    private val usbManager by lazy { 
        context.getSystemService(Context.USB_SERVICE) as UsbManager 
    }
    
    /**
     * 检查设备USB Host支持情况
     */
    fun checkUsbHostSupport(): UsbCompatibilityResult {
        Log.d(TAG, "开始检查USB Host兼容性")
        
        val result = UsbCompatibilityResult()
        
        // 1. 检查硬件支持
        result.hasUsbHostFeature = context.packageManager.hasSystemFeature(
            PackageManager.FEATURE_USB_HOST
        )
        
        // 2. 检查API级别
        result.apiLevelSupported = Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB_MR1
        
        // 3. 检查USB管理器
        result.usbManagerAvailable = try {
            usbManager != null
        } catch (e: Exception) {
            Log.e(TAG, "USB管理器不可用", e)
            false
        }
        
        // 4. 检查当前连接的USB设备
        result.connectedDevices = try {
            usbManager.deviceList.size
        } catch (e: Exception) {
            Log.e(TAG, "无法获取USB设备列表", e)
            0
        }
        
        // 5. 检查存储权限
        result.storagePermissionStatus = checkStoragePermissions()
        
        // 6. 生成兼容性评分
        result.compatibilityScore = calculateCompatibilityScore(result)
        
        // 7. 生成建议
        result.recommendations = generateRecommendations(result)
        
        Log.d(TAG, "USB兼容性检查完成: $result")
        return result
    }
    
    /**
     * 检查特定USB设备的权限状态
     */
    fun checkDevicePermission(device: UsbDevice): DevicePermissionStatus {
        return try {
            val hasPermission = usbManager.hasPermission(device)
            DevicePermissionStatus(
                deviceName = device.deviceName,
                hasPermission = hasPermission,
                deviceClass = device.deviceClass,
                vendorId = device.vendorId,
                productId = device.productId,
                isStorageDevice = isStorageDevice(device)
            )
        } catch (e: Exception) {
            Log.e(TAG, "检查设备权限失败", e)
            DevicePermissionStatus(
                deviceName = device.deviceName,
                hasPermission = false,
                error = e.message
            )
        }
    }
    
    /**
     * 检查存储权限状态
     */
    private fun checkStoragePermissions(): StoragePermissionStatus {
        val status = StoragePermissionStatus()
        
        // 检查基础存储权限
        status.hasReadExternalStorage = context.checkSelfPermission(
            android.Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
        
        status.hasWriteExternalStorage = context.checkSelfPermission(
            android.Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
        
        // Android 11+ 特殊权限检查
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            status.hasManageExternalStorage = context.checkSelfPermission(
                android.Manifest.permission.MANAGE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
            
            status.needsScopedStorage = true
        }
        
        return status
    }
    
    /**
     * 判断是否为存储设备
     */
    private fun isStorageDevice(device: UsbDevice): Boolean {
        return device.deviceClass == android.hardware.usb.UsbConstants.USB_CLASS_MASS_STORAGE ||
                device.deviceName.contains("storage", ignoreCase = true) ||
                device.deviceName.contains("disk", ignoreCase = true)
    }
    
    /**
     * 计算兼容性评分 (0-100)
     */
    private fun calculateCompatibilityScore(result: UsbCompatibilityResult): Int {
        var score = 0
        
        if (result.hasUsbHostFeature) score += 40
        if (result.apiLevelSupported) score += 20
        if (result.usbManagerAvailable) score += 20
        if (result.storagePermissionStatus.hasReadExternalStorage) score += 10
        if (result.storagePermissionStatus.hasWriteExternalStorage) score += 5
        if (result.storagePermissionStatus.hasManageExternalStorage) score += 5
        
        return score
    }
    
    /**
     * 生成改进建议
     */
    private fun generateRecommendations(result: UsbCompatibilityResult): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (!result.hasUsbHostFeature) {
            recommendations.add("设备不支持USB Host功能，建议使用手动文件选择")
        }
        
        if (!result.apiLevelSupported) {
            recommendations.add("Android版本过低，建议升级到Android 3.1或更高版本")
        }
        
        if (!result.storagePermissionStatus.hasReadExternalStorage) {
            recommendations.add("需要授予存储读取权限")
        }
        
        if (result.storagePermissionStatus.needsScopedStorage && 
            !result.storagePermissionStatus.hasManageExternalStorage) {
            recommendations.add("Android 11+需要特殊存储权限，建议使用SAF文件访问")
        }
        
        if (result.compatibilityScore < 60) {
            recommendations.add("设备兼容性较低，建议使用备用文件访问方案")
        }
        
        return recommendations
    }
    
    /**
     * 获取用户友好的兼容性描述
     */
    fun getCompatibilityDescription(score: Int): String {
        return when {
            score >= 80 -> "完全兼容 - USB功能可以正常使用"
            score >= 60 -> "基本兼容 - USB功能可能需要额外权限"
            score >= 40 -> "部分兼容 - 建议使用备用方案"
            else -> "不兼容 - 请使用手动文件选择"
        }
    }
}

/**
 * USB兼容性检查结果
 */
data class UsbCompatibilityResult(
    var hasUsbHostFeature: Boolean = false,
    var apiLevelSupported: Boolean = false,
    var usbManagerAvailable: Boolean = false,
    var connectedDevices: Int = 0,
    var storagePermissionStatus: StoragePermissionStatus = StoragePermissionStatus(),
    var compatibilityScore: Int = 0,
    var recommendations: List<String> = emptyList()
)

/**
 * 设备权限状态
 */
data class DevicePermissionStatus(
    val deviceName: String,
    val hasPermission: Boolean,
    val deviceClass: Int = 0,
    val vendorId: Int = 0,
    val productId: Int = 0,
    val isStorageDevice: Boolean = false,
    val error: String? = null
)

/**
 * 存储权限状态
 */
data class StoragePermissionStatus(
    var hasReadExternalStorage: Boolean = false,
    var hasWriteExternalStorage: Boolean = false,
    var hasManageExternalStorage: Boolean = false,
    var needsScopedStorage: Boolean = false
)
