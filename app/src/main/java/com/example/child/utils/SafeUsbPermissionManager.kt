package com.example.child.utils

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 安全的USB权限管理器
 * 处理USB权限请求和状态管理，避免崩溃问题
 */
@Singleton
class SafeUsbPermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "SafeUsbPermissionManager"
        const val ACTION_USB_PERMISSION = "com.example.child.USB_PERMISSION"
    }
    
    private val usbManager by lazy { 
        context.getSystemService(Context.USB_SERVICE) as UsbManager 
    }
    
    // 权限状态流
    private val _permissionStatus = MutableStateFlow<UsbPermissionState>(UsbPermissionState.Idle)
    val permissionStatus: StateFlow<UsbPermissionState> = _permissionStatus.asStateFlow()
    
    // 权限请求结果流
    private val _permissionResults = MutableStateFlow<UsbPermissionResult?>(null)
    val permissionResults: StateFlow<UsbPermissionResult?> = _permissionResults.asStateFlow()
    
    // 权限广播接收器
    private val permissionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (ACTION_USB_PERMISSION == intent.action) {
                handlePermissionResult(intent)
            }
        }
    }
    
    private var isReceiverRegistered = false
    
    /**
     * 初始化权限管理器
     */
    fun initialize() {
        try {
            if (!isReceiverRegistered) {
                val filter = IntentFilter(ACTION_USB_PERMISSION)
                context.registerReceiver(permissionReceiver, filter)
                isReceiverRegistered = true
                Log.d(TAG, "权限管理器初始化成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "权限管理器初始化失败", e)
        }
    }
    
    /**
     * 清理权限管理器
     */
    fun cleanup() {
        try {
            if (isReceiverRegistered) {
                context.unregisterReceiver(permissionReceiver)
                isReceiverRegistered = false
                Log.d(TAG, "权限管理器已清理")
            }
        } catch (e: Exception) {
            Log.e(TAG, "权限管理器清理失败", e)
        }
    }
    
    /**
     * 检查设备权限
     */
    fun checkPermission(device: UsbDevice): Boolean {
        return try {
            usbManager.hasPermission(device)
        } catch (e: Exception) {
            Log.e(TAG, "检查权限失败: ${device.deviceName}", e)
            false
        }
    }
    
    /**
     * 安全地请求USB权限
     */
    fun requestPermission(device: UsbDevice) {
        try {
            _permissionStatus.value = UsbPermissionState.Requesting(device.deviceName)
            
            if (usbManager.hasPermission(device)) {
                // 已有权限，直接返回成功
                _permissionResults.value = UsbPermissionResult(
                    device = device,
                    granted = true,
                    timestamp = System.currentTimeMillis()
                )
                _permissionStatus.value = UsbPermissionState.Granted(device.deviceName)
                return
            }
            
            // 创建权限请求Intent
            val permissionIntent = PendingIntent.getBroadcast(
                context,
                0,
                Intent(ACTION_USB_PERMISSION),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 请求权限
            usbManager.requestPermission(device, permissionIntent)
            Log.d(TAG, "已发送权限请求: ${device.deviceName}")
            
        } catch (e: Exception) {
            Log.e(TAG, "请求权限失败: ${device.deviceName}", e)
            _permissionStatus.value = UsbPermissionState.Error(e.message ?: "权限请求失败")
            _permissionResults.value = UsbPermissionResult(
                device = device,
                granted = false,
                error = e.message,
                timestamp = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 处理权限请求结果
     */
    private fun handlePermissionResult(intent: Intent) {
        try {
            val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
            val granted = intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)
            
            device?.let {
                Log.d(TAG, "权限请求结果: ${it.deviceName}, 授予: $granted")
                
                _permissionResults.value = UsbPermissionResult(
                    device = it,
                    granted = granted,
                    timestamp = System.currentTimeMillis()
                )
                
                _permissionStatus.value = if (granted) {
                    UsbPermissionState.Granted(it.deviceName)
                } else {
                    UsbPermissionState.Denied(it.deviceName)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理权限结果失败", e)
            _permissionStatus.value = UsbPermissionState.Error(e.message ?: "处理权限结果失败")
        }
    }
    
    /**
     * 获取所有USB设备的权限状态
     */
    fun getAllDevicePermissions(): Map<String, Boolean> {
        return try {
            usbManager.deviceList.mapValues { (_, device) ->
                usbManager.hasPermission(device)
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取设备权限状态失败", e)
            emptyMap()
        }
    }
    
    /**
     * 批量请求多个设备的权限
     */
    fun requestMultiplePermissions(devices: List<UsbDevice>) {
        devices.forEach { device ->
            if (!checkPermission(device)) {
                requestPermission(device)
                // 添加延迟避免同时请求多个权限
                Thread.sleep(500)
            }
        }
    }
    
    /**
     * 清除权限结果
     */
    fun clearPermissionResults() {
        _permissionResults.value = null
        _permissionStatus.value = UsbPermissionState.Idle
    }
    
    /**
     * 获取权限状态描述
     */
    fun getPermissionStatusDescription(state: UsbPermissionState): String {
        return when (state) {
            is UsbPermissionState.Idle -> "等待权限操作"
            is UsbPermissionState.Requesting -> "正在请求权限: ${state.deviceName}"
            is UsbPermissionState.Granted -> "权限已授予: ${state.deviceName}"
            is UsbPermissionState.Denied -> "权限被拒绝: ${state.deviceName}"
            is UsbPermissionState.Error -> "权限错误: ${state.message}"
        }
    }
}

/**
 * USB权限状态
 */
sealed class UsbPermissionState {
    object Idle : UsbPermissionState()
    data class Requesting(val deviceName: String) : UsbPermissionState()
    data class Granted(val deviceName: String) : UsbPermissionState()
    data class Denied(val deviceName: String) : UsbPermissionState()
    data class Error(val message: String) : UsbPermissionState()
}

/**
 * USB权限请求结果
 */
data class UsbPermissionResult(
    val device: UsbDevice,
    val granted: Boolean,
    val error: String? = null,
    val timestamp: Long
)
