package com.example.child.utils

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 安全的服务状态检查器
 * 使用更安全的方法检查服务状态，避免使用已弃用的API
 */
@Singleton
class SafeServiceChecker @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "SafeServiceChecker"
    }
    
    // 服务状态流
    private val _serviceState = MutableStateFlow<SimpleServiceState>(SimpleServiceState.Unknown)
    val serviceState: StateFlow<SimpleServiceState> = _serviceState.asStateFlow()
    
    // 服务统计信息
    private val _serviceStats = MutableStateFlow<SimpleServiceStats>(SimpleServiceStats())
    val serviceStats: StateFlow<SimpleServiceStats> = _serviceStats.asStateFlow()
    
    /**
     * 安全地检查服务是否运行
     * 使用PendingIntent方法，避免使用已弃用的getRunningServices
     */
    fun checkServiceStatus(): Boolean {
        return try {
            Log.d(TAG, "开始检查服务状态")
            
            val intent = Intent(context, OptimizedUsbMonitorService::class.java)
            val pendingIntent = PendingIntent.getService(
                context, 
                0, 
                intent, 
                PendingIntent.FLAG_NO_CREATE or PendingIntent.FLAG_IMMUTABLE
            )
            
            val isRunning = pendingIntent != null
            
            // 更新状态
            _serviceState.value = if (isRunning) {
                SimpleServiceState.Running
            } else {
                SimpleServiceState.Stopped
            }
            
            Log.d(TAG, "服务状态检查完成: $isRunning")
            isRunning
            
        } catch (e: Exception) {
            Log.e(TAG, "检查服务状态失败", e)
            _serviceState.value = SimpleServiceState.Error(e.message ?: "检查失败")
            false
        }
    }
    
    /**
     * 安全地启动服务
     */
    fun startService(): Boolean {
        return try {
            Log.d(TAG, "尝试启动服务")
            
            val intent = Intent(context, OptimizedUsbMonitorService::class.java)
            context.startForegroundService(intent)
            
            _serviceState.value = SimpleServiceState.Starting
            updateServiceStats(isStarting = true)
            
            Log.d(TAG, "服务启动请求已发送")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "启动服务失败", e)
            _serviceState.value = SimpleServiceState.Error(e.message ?: "启动失败")
            false
        }
    }
    
    /**
     * 安全地停止服务
     */
    fun stopService(): Boolean {
        return try {
            Log.d(TAG, "尝试停止服务")
            
            val intent = Intent(context, OptimizedUsbMonitorService::class.java)
            context.stopService(intent)
            
            _serviceState.value = SimpleServiceState.Stopping
            updateServiceStats(isStarting = false)
            
            Log.d(TAG, "服务停止请求已发送")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "停止服务失败", e)
            _serviceState.value = SimpleServiceState.Error(e.message ?: "停止失败")
            false
        }
    }
    
    /**
     * 获取服务健康状态
     */
    fun getServiceHealth(): SimpleServiceHealth {
        return try {
            val isRunning = checkServiceStatus()
            val currentTime = System.currentTimeMillis()
            
            SimpleServiceHealth(
                isRunning = isRunning,
                isHealthy = true, // 简化的健康检查
                lastCheckTime = currentTime,
                uptime = if (isRunning) estimateUptime() else 0L
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "获取服务健康状态失败", e)
            SimpleServiceHealth(
                isRunning = false,
                isHealthy = false,
                lastCheckTime = System.currentTimeMillis(),
                uptime = 0L,
                error = e.message
            )
        }
    }
    
    /**
     * 估算服务运行时间（简化版本）
     */
    private fun estimateUptime(): Long {
        val stats = _serviceStats.value
        return if (stats.lastStartTime > 0) {
            System.currentTimeMillis() - stats.lastStartTime
        } else {
            0L
        }
    }
    
    /**
     * 更新服务统计信息
     */
    private fun updateServiceStats(isStarting: Boolean) {
        val currentStats = _serviceStats.value
        val newStats = if (isStarting) {
            currentStats.copy(
                startCount = currentStats.startCount + 1,
                lastStartTime = System.currentTimeMillis()
            )
        } else {
            currentStats.copy(
                stopCount = currentStats.stopCount + 1,
                lastStopTime = System.currentTimeMillis()
            )
        }
        
        _serviceStats.value = newStats
    }
    
    /**
     * 获取服务状态描述
     */
    fun getServiceStateDescription(state: SimpleServiceState): String {
        return when (state) {
            is SimpleServiceState.Unknown -> "状态未知"
            is SimpleServiceState.Stopped -> "服务已停止"
            is SimpleServiceState.Starting -> "服务启动中..."
            is SimpleServiceState.Running -> "服务运行中"
            is SimpleServiceState.Stopping -> "服务停止中..."
            is SimpleServiceState.Error -> "服务错误: ${state.message}"
        }
    }
    
    /**
     * 重置统计信息
     */
    fun resetStats() {
        _serviceStats.value = SimpleServiceStats()
        Log.d(TAG, "服务统计信息已重置")
    }
}

/**
 * 简化的服务状态
 */
sealed class SimpleServiceState {
    object Unknown : SimpleServiceState()
    object Stopped : SimpleServiceState()
    object Starting : SimpleServiceState()
    object Running : SimpleServiceState()
    object Stopping : SimpleServiceState()
    data class Error(val message: String) : SimpleServiceState()
}

/**
 * 简化的服务统计信息
 */
data class SimpleServiceStats(
    val startCount: Int = 0,
    val stopCount: Int = 0,
    val lastStartTime: Long = 0L,
    val lastStopTime: Long = 0L
)

/**
 * 简化的服务健康状态
 */
data class SimpleServiceHealth(
    val isRunning: Boolean,
    val isHealthy: Boolean,
    val lastCheckTime: Long,
    val uptime: Long,
    val error: String? = null
)
