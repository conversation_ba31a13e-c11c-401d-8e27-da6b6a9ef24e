package com.example.child.utils

import android.content.Intent
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SAF Activity桥接器
 * 用于在Activity和ViewModel之间传递SAF相关的事件
 */
@Singleton
class SafActivityBridge @Inject constructor() {
    
    companion object {
        private const val TAG = "SafActivityBridge"
    }
    
    // 目录选择请求事件
    private val _directoryPickerRequests = MutableSharedFlow<Intent>()
    val directoryPickerRequests: SharedFlow<Intent> = _directoryPickerRequests.asSharedFlow()
    
    // 目录选择结果事件
    private val _directorySelectionResults = MutableSharedFlow<Uri>()
    val directorySelectionResults: SharedFlow<Uri> = _directorySelectionResults.asSharedFlow()
    
    // Activity引用（用于启动Intent）
    private var activityCallback: ((Intent) -> Unit)? = null
    
    /**
     * 设置Activity回调
     */
    fun setActivityCallback(callback: (Intent) -> Unit) {
        this.activityCallback = callback
        Log.d(TAG, "Activity回调已设置")
    }
    
    /**
     * 清除Activity回调
     */
    fun clearActivityCallback() {
        this.activityCallback = null
        Log.d(TAG, "Activity回调已清除")
    }
    
    /**
     * 请求目录选择
     */
    suspend fun requestDirectoryPicker(intent: Intent) {
        try {
            activityCallback?.invoke(intent) ?: run {
                Log.w(TAG, "Activity回调未设置，无法启动目录选择器")
                return
            }
            
            // 发送请求事件
            _directoryPickerRequests.emit(intent)
            Log.d(TAG, "目录选择请求已发送")
            
        } catch (e: Exception) {
            Log.e(TAG, "请求目录选择失败", e)
        }
    }
    
    /**
     * 处理目录选择结果
     */
    suspend fun handleDirectorySelectionResult(uri: Uri) {
        try {
            _directorySelectionResults.emit(uri)
            Log.d(TAG, "目录选择结果已处理: $uri")
        } catch (e: Exception) {
            Log.e(TAG, "处理目录选择结果失败", e)
        }
    }
    
    /**
     * 检查Activity回调是否可用
     */
    fun isActivityCallbackAvailable(): Boolean {
        return activityCallback != null
    }
}
