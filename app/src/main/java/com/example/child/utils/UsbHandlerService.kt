package com.example.child.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.child.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * USB处理服务
 * 处理USB相关事件，不使用Hilt依赖注入
 */
class UsbHandlerService : Service() {
    
    companion object {
        private const val TAG = "UsbHandlerService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "usb_handler_channel"
        
        // Action常量
        const val ACTION_DEVICE_ATTACHED = "com.example.child.ACTION_DEVICE_ATTACHED"
        const val ACTION_DEVICE_DETACHED = "com.example.child.ACTION_DEVICE_DETACHED"
        const val ACTION_MEDIA_MOUNTED = "com.example.child.ACTION_MEDIA_MOUNTED"
        const val ACTION_MEDIA_UNMOUNTED = "com.example.child.ACTION_MEDIA_UNMOUNTED"
        const val ACTION_MEDIA_REMOVED = "com.example.child.ACTION_MEDIA_REMOVED"
        const val ACTION_PERMISSION_RESULT = "com.example.child.ACTION_PERMISSION_RESULT"
        
        // Extra常量
        const val EXTRA_USB_DEVICE = "extra_usb_device"
        const val EXTRA_MEDIA_PATH = "extra_media_path"
        const val EXTRA_PERMISSION_GRANTED = "extra_permission_granted"
    }
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var currentJob: Job? = null
    
    // 手动获取依赖（避免Hilt）
    private val usbEventProcessor by lazy { UsbEventProcessor() }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "USB处理服务创建")
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "USB处理服务启动: ${intent?.action}")
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification("USB监听服务运行中"))
        
        intent?.let { processIntent(it) }
        
        // 返回START_STICKY确保服务重启
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "USB处理服务销毁")
        
        // 取消所有协程
        currentJob?.cancel()
        serviceScope.cancel()
    }
    
    /**
     * 处理Intent
     */
    private fun processIntent(intent: Intent) {
        // 取消之前的任务
        currentJob?.cancel()
        
        // 启动新任务
        currentJob = serviceScope.launch {
            try {
                when (intent.action) {
                    ACTION_DEVICE_ATTACHED -> {
                        val device = intent.getParcelableExtra<UsbDevice>(EXTRA_USB_DEVICE)
                        device?.let { usbEventProcessor.handleDeviceAttached(this@UsbHandlerService, it) }
                    }
                    ACTION_DEVICE_DETACHED -> {
                        val device = intent.getParcelableExtra<UsbDevice>(EXTRA_USB_DEVICE)
                        device?.let { usbEventProcessor.handleDeviceDetached(this@UsbHandlerService, it) }
                    }
                    ACTION_MEDIA_MOUNTED -> {
                        val path = intent.getStringExtra(EXTRA_MEDIA_PATH)
                        path?.let { usbEventProcessor.handleMediaMounted(this@UsbHandlerService, it) }
                    }
                    ACTION_MEDIA_UNMOUNTED -> {
                        val path = intent.getStringExtra(EXTRA_MEDIA_PATH)
                        path?.let { usbEventProcessor.handleMediaUnmounted(this@UsbHandlerService, it) }
                    }
                    ACTION_MEDIA_REMOVED -> {
                        val path = intent.getStringExtra(EXTRA_MEDIA_PATH)
                        path?.let { usbEventProcessor.handleMediaRemoved(this@UsbHandlerService, it) }
                    }
                    ACTION_PERMISSION_RESULT -> {
                        val device = intent.getParcelableExtra<UsbDevice>(EXTRA_USB_DEVICE)
                        val granted = intent.getBooleanExtra(EXTRA_PERMISSION_GRANTED, false)
                        device?.let { usbEventProcessor.handlePermissionResult(this@UsbHandlerService, it, granted) }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理USB事件失败: ${intent.action}", e)
            }
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "USB处理服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "USB设备监听和处理服务"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "通知渠道已创建")
        }
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(content: String): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("儿童成长分析")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    /**
     * 更新通知
     */
    fun updateNotification(content: String) {
        try {
            val notification = createNotification(content)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            Log.e(TAG, "更新通知失败", e)
        }
    }
}

/**
 * USB事件处理器
 * 处理具体的USB事件逻辑
 */
class UsbEventProcessor {
    
    companion object {
        private const val TAG = "UsbEventProcessor"
    }
    
    /**
     * 处理设备插入
     */
    suspend fun handleDeviceAttached(context: Context, device: UsbDevice) {
        try {
            Log.d(TAG, "处理设备插入: ${device.deviceName}")
            
            // 更新服务通知
            if (context is UsbHandlerService) {
                context.updateNotification("检测到USB设备: ${device.deviceName}")
            }
            
            // 检查是否为存储设备
            if (isStorageDevice(device)) {
                Log.d(TAG, "检测到存储设备: ${device.deviceName}")
                
                // 这里可以触发自动扫描
                // 暂时只记录日志
                Log.d(TAG, "存储设备插入，可以开始文件扫描")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理设备插入失败", e)
        }
    }
    
    /**
     * 处理设备拔出
     */
    suspend fun handleDeviceDetached(context: Context, device: UsbDevice) {
        try {
            Log.d(TAG, "处理设备拔出: ${device.deviceName}")
            
            // 更新服务通知
            if (context is UsbHandlerService) {
                context.updateNotification("USB设备已拔出: ${device.deviceName}")
            }
            
            // 清理相关资源
            Log.d(TAG, "清理设备相关资源: ${device.deviceName}")
            
        } catch (e: Exception) {
            Log.e(TAG, "处理设备拔出失败", e)
        }
    }
    
    /**
     * 处理媒体挂载
     */
    suspend fun handleMediaMounted(context: Context, path: String) {
        try {
            Log.d(TAG, "处理媒体挂载: $path")
            
            // 更新服务通知
            if (context is UsbHandlerService) {
                context.updateNotification("媒体已挂载: $path")
            }
            
            // 这里可以开始文件扫描
            Log.d(TAG, "媒体挂载，可以开始文件扫描: $path")
            
        } catch (e: Exception) {
            Log.e(TAG, "处理媒体挂载失败", e)
        }
    }
    
    /**
     * 处理媒体卸载
     */
    suspend fun handleMediaUnmounted(context: Context, path: String) {
        try {
            Log.d(TAG, "处理媒体卸载: $path")
            
            // 更新服务通知
            if (context is UsbHandlerService) {
                context.updateNotification("媒体已卸载: $path")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理媒体卸载失败", e)
        }
    }
    
    /**
     * 处理媒体移除
     */
    suspend fun handleMediaRemoved(context: Context, path: String) {
        try {
            Log.d(TAG, "处理媒体移除: $path")
            
            // 更新服务通知
            if (context is UsbHandlerService) {
                context.updateNotification("媒体已移除: $path")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理媒体移除失败", e)
        }
    }
    
    /**
     * 处理权限结果
     */
    suspend fun handlePermissionResult(context: Context, device: UsbDevice, granted: Boolean) {
        try {
            Log.d(TAG, "处理权限结果: ${device.deviceName}, 授予: $granted")
            
            // 更新服务通知
            if (context is UsbHandlerService) {
                val message = if (granted) "USB权限已授予: ${device.deviceName}" else "USB权限被拒绝: ${device.deviceName}"
                context.updateNotification(message)
            }
            
            if (granted) {
                // 权限授予后可以开始操作设备
                Log.d(TAG, "USB权限已授予，可以开始设备操作")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理权限结果失败", e)
        }
    }
    
    /**
     * 判断是否为存储设备
     */
    private fun isStorageDevice(device: UsbDevice): Boolean {
        return device.deviceClass == android.hardware.usb.UsbConstants.USB_CLASS_MASS_STORAGE ||
                device.deviceName.contains("storage", ignoreCase = true) ||
                device.deviceName.contains("disk", ignoreCase = true)
    }
}
