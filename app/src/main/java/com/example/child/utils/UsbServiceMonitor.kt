package com.example.child.utils

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * USB服务状态监控器
 * 监控USB相关服务的运行状态
 */
@Singleton
class UsbServiceMonitor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "UsbServiceMonitor"
    }
    
    // 服务状态流
    private val _serviceState = MutableStateFlow<UsbServiceState>(UsbServiceState.Stopped)
    val serviceState: StateFlow<UsbServiceState> = _serviceState.asStateFlow()
    
    // 服务统计信息
    private val _serviceStats = MutableStateFlow<UsbServiceStats>(UsbServiceStats())
    val serviceStats: StateFlow<UsbServiceStats> = _serviceStats.asStateFlow()
    
    /**
     * 启动USB监听服务
     */
    fun startUsbMonitorService(): Boolean {
        return try {
            val intent = Intent(context, OptimizedUsbMonitorService::class.java)
            context.startForegroundService(intent)
            
            _serviceState.value = UsbServiceState.Starting
            updateServiceStats(isRunning = true)
            
            Log.d(TAG, "USB监听服务启动请求已发送")
            true
        } catch (e: Exception) {
            Log.e(TAG, "启动USB监听服务失败", e)
            _serviceState.value = UsbServiceState.Error(e.message ?: "启动失败")
            false
        }
    }
    
    /**
     * 停止USB监听服务
     */
    fun stopUsbMonitorService(): Boolean {
        return try {
            val intent = Intent(context, OptimizedUsbMonitorService::class.java)
            context.stopService(intent)
            
            _serviceState.value = UsbServiceState.Stopping
            updateServiceStats(isRunning = false)
            
            Log.d(TAG, "USB监听服务停止请求已发送")
            true
        } catch (e: Exception) {
            Log.e(TAG, "停止USB监听服务失败", e)
            _serviceState.value = UsbServiceState.Error(e.message ?: "停止失败")
            false
        }
    }
    
    /**
     * 检查服务是否运行
     */
    fun isServiceRunning(): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            val isRunning = runningServices.any { serviceInfo ->
                serviceInfo.service.className == OptimizedUsbMonitorService::class.java.name
            }
            
            // 更新状态
            _serviceState.value = if (isRunning) {
                UsbServiceState.Running
            } else {
                UsbServiceState.Stopped
            }
            
            Log.d(TAG, "服务运行状态检查: $isRunning")
            isRunning
            
        } catch (e: Exception) {
            Log.e(TAG, "检查服务运行状态失败", e)
            _serviceState.value = UsbServiceState.Error(e.message ?: "状态检查失败")
            false
        }
    }
    
    /**
     * 重启USB监听服务
     */
    fun restartUsbMonitorService(): Boolean {
        return try {
            Log.d(TAG, "重启USB监听服务")
            
            // 先停止服务
            stopUsbMonitorService()
            
            // 等待一段时间
            Thread.sleep(1000)
            
            // 再启动服务
            startUsbMonitorService()
            
        } catch (e: Exception) {
            Log.e(TAG, "重启USB监听服务失败", e)
            _serviceState.value = UsbServiceState.Error(e.message ?: "重启失败")
            false
        }
    }
    
    /**
     * 检查服务健康状态
     */
    fun checkServiceHealth(): ServiceHealthStatus {
        return try {
            val isRunning = isServiceRunning()
            val currentTime = System.currentTimeMillis()
            
            val healthStatus = ServiceHealthStatus(
                isRunning = isRunning,
                lastCheckTime = currentTime,
                isHealthy = isRunning, // 简单的健康检查
                uptime = if (isRunning) getServiceUptime() else 0L
            )
            
            Log.d(TAG, "服务健康检查: $healthStatus")
            healthStatus
            
        } catch (e: Exception) {
            Log.e(TAG, "服务健康检查失败", e)
            ServiceHealthStatus(
                isRunning = false,
                lastCheckTime = System.currentTimeMillis(),
                isHealthy = false,
                uptime = 0L,
                error = e.message
            )
        }
    }
    
    /**
     * 获取服务运行时间
     */
    private fun getServiceUptime(): Long {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            val serviceInfo = runningServices.find { serviceInfo ->
                serviceInfo.service.className == OptimizedUsbMonitorService::class.java.name
            }
            
            serviceInfo?.let {
                System.currentTimeMillis() - it.activeSince
            } ?: 0L
            
        } catch (e: Exception) {
            Log.e(TAG, "获取服务运行时间失败", e)
            0L
        }
    }
    
    /**
     * 更新服务统计信息
     */
    private fun updateServiceStats(isRunning: Boolean) {
        val currentStats = _serviceStats.value
        val newStats = if (isRunning) {
            currentStats.copy(
                startCount = currentStats.startCount + 1,
                lastStartTime = System.currentTimeMillis()
            )
        } else {
            currentStats.copy(
                stopCount = currentStats.stopCount + 1,
                lastStopTime = System.currentTimeMillis()
            )
        }
        
        _serviceStats.value = newStats
    }
    
    /**
     * 获取服务状态描述
     */
    fun getServiceStateDescription(state: UsbServiceState): String {
        return when (state) {
            is UsbServiceState.Stopped -> "服务已停止"
            is UsbServiceState.Starting -> "服务启动中..."
            is UsbServiceState.Running -> "服务运行中"
            is UsbServiceState.Stopping -> "服务停止中..."
            is UsbServiceState.Error -> "服务错误: ${state.message}"
        }
    }
    
    /**
     * 重置统计信息
     */
    fun resetStats() {
        _serviceStats.value = UsbServiceStats()
        Log.d(TAG, "服务统计信息已重置")
    }
}

/**
 * USB服务状态
 */
sealed class UsbServiceState {
    object Stopped : UsbServiceState()
    object Starting : UsbServiceState()
    object Running : UsbServiceState()
    object Stopping : UsbServiceState()
    data class Error(val message: String) : UsbServiceState()
}

/**
 * USB服务统计信息
 */
data class UsbServiceStats(
    val startCount: Int = 0,
    val stopCount: Int = 0,
    val lastStartTime: Long = 0L,
    val lastStopTime: Long = 0L
)

/**
 * 服务健康状态
 */
data class ServiceHealthStatus(
    val isRunning: Boolean,
    val lastCheckTime: Long,
    val isHealthy: Boolean,
    val uptime: Long,
    val error: String? = null
)
