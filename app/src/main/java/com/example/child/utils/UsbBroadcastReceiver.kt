package com.example.child.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * USB设备插入/拔出广播接收器
 * 监听USB设备状态变化，自动触发文件扫描
 */
class UsbBroadcastReceiver : BroadcastReceiver() {

    // 注意：BroadcastReceiver不能使用Hilt依赖注入
    // 需要手动获取依赖或通过其他方式注入

    companion object {
        private const val TAG = "UsbBroadcastReceiver"
        const val ACTION_USB_PERMISSION = "com.example.child.USB_PERMISSION"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到广播: ${intent.action}")

        when (intent.action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                handleUsbDeviceAttached(context, intent)
            }
            UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                handleUsbDeviceDetached(context, intent)
            }
            Intent.ACTION_MEDIA_MOUNTED -> {
                handleMediaMounted(context, intent)
            }
            Intent.ACTION_MEDIA_UNMOUNTED,
            Intent.ACTION_MEDIA_REMOVED -> {
                handleMediaUnmounted(context, intent)
            }
            ACTION_USB_PERMISSION -> {
                handleUsbPermission(context, intent)
            }
        }
    }

    /**
     * 处理USB设备插入事件
     */
    private fun handleUsbDeviceAttached(context: Context, intent: Intent) {
        val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
        device?.let {
            Log.d(TAG, "USB设备已插入: ${it.deviceName}")

            // 检查是否为存储设备
            if (isStorageDevice(it)) {
                Log.d(TAG, "检测到存储设备，开始处理...")

                // 启动USB监听服务
                startUsbMonitorService(context)

                // 发送广播通知应用扫描设备
                val scanIntent = Intent("com.example.child.ACTION_SCAN_USB_DEVICE")
                context.sendBroadcast(scanIntent)
            }
        }
    }

    /**
     * 处理USB设备拔出事件
     */
    private fun handleUsbDeviceDetached(context: Context, intent: Intent) {
        val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
        device?.let {
            Log.d(TAG, "USB设备已拔出: ${it.deviceName}")

            // 发送广播通知应用设备已拔出
            val detachIntent = Intent("com.example.child.ACTION_USB_DEVICE_DETACHED")
            context.sendBroadcast(detachIntent)
        }
    }

    /**
     * 处理存储媒体挂载事件
     */
    private fun handleMediaMounted(context: Context, intent: Intent) {
        val path = intent.data?.path
        Log.d(TAG, "存储媒体已挂载: $path")

        path?.let {
            // 检查是否为可移动存储
            if (it.contains("usb") || it.contains("sdcard") || it.contains("external")) {
                Log.d(TAG, "检测到可移动存储挂载，开始扫描文件...")

                // 发送广播通知应用媒体已挂载
                val mountIntent = Intent("com.example.child.ACTION_MEDIA_MOUNTED")
                mountIntent.putExtra("path", it)
                context.sendBroadcast(mountIntent)
            }
        }
    }

    /**
     * 处理存储媒体卸载事件
     */
    private fun handleMediaUnmounted(context: Context, intent: Intent) {
        val path = intent.data?.path
        Log.d(TAG, "存储媒体已卸载: $path")

        // 发送广播通知应用媒体已卸载
        val unmountIntent = Intent("com.example.child.ACTION_MEDIA_UNMOUNTED")
        context.sendBroadcast(unmountIntent)
    }

    /**
     * 处理USB权限响应
     */
    private fun handleUsbPermission(context: Context, intent: Intent) {
        val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
        val granted = intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)

        device?.let {
            if (granted) {
                Log.d(TAG, "USB权限已授予: ${it.deviceName}")
                // 发送广播通知应用权限已授予
                val permissionIntent = Intent("com.example.child.ACTION_USB_PERMISSION_GRANTED")
                context.sendBroadcast(permissionIntent)
            } else {
                Log.w(TAG, "USB权限被拒绝: ${it.deviceName}")
            }
        }
    }

    /**
     * 检查是否为存储设备
     */
    private fun isStorageDevice(device: UsbDevice): Boolean {
        return device.deviceClass == android.hardware.usb.UsbConstants.USB_CLASS_MASS_STORAGE ||
                device.deviceName.contains("storage", ignoreCase = true) ||
                device.deviceName.contains("disk", ignoreCase = true) ||
                device.deviceName.contains("usb", ignoreCase = true)
    }

    /**
     * 启动USB监听服务
     */
    private fun startUsbMonitorService(context: Context) {
        val serviceIntent = Intent(context, UsbMonitorService::class.java)
        context.startService(serviceIntent)
    }
}
