package com.example.child.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.child.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 优化的USB监听服务
 * 提供稳定的USB设备监听功能，不使用Hilt依赖注入
 */
class OptimizedUsbMonitorService : Service() {
    
    companion object {
        private const val TAG = "OptimizedUsbMonitorService"
        private const val NOTIFICATION_ID = 1000
        private const val CHANNEL_ID = "usb_monitor_channel"
        private const val HEALTH_CHECK_INTERVAL = 30000L // 30秒
    }
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var usbBroadcastReceiver: SafeUsbBroadcastReceiver? = null
    private var isReceiverRegistered = false
    
    // 服务状态
    private var isServiceHealthy = true
    private var lastHealthCheck = System.currentTimeMillis()
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "优化USB监听服务创建")
        
        createNotificationChannel()
        startHealthCheck()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "优化USB监听服务启动")
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification("USB监听服务运行中"))
        
        // 注册USB广播接收器
        registerUsbReceiver()
        
        // 返回START_STICKY确保服务重启
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "优化USB监听服务销毁")
        
        // 注销广播接收器
        unregisterUsbReceiver()
        
        // 取消所有协程
        serviceScope.cancel()
    }
    
    /**
     * 注册USB广播接收器
     */
    private fun registerUsbReceiver() {
        try {
            if (!isReceiverRegistered) {
                usbBroadcastReceiver = SafeUsbBroadcastReceiver()
                
                val filter = IntentFilter().apply {
                    addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
                    addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
                    addAction("android.intent.action.MEDIA_MOUNTED")
                    addAction("android.intent.action.MEDIA_UNMOUNTED")
                    addAction("android.intent.action.MEDIA_REMOVED")
                    addAction(SafeUsbPermissionManager.ACTION_USB_PERMISSION)
                    addDataScheme("file")
                }
                
                registerReceiver(usbBroadcastReceiver, filter)
                isReceiverRegistered = true
                
                Log.d(TAG, "USB广播接收器注册成功")
                updateNotification("USB监听已启动")
            }
        } catch (e: Exception) {
            Log.e(TAG, "注册USB广播接收器失败", e)
            isServiceHealthy = false
            updateNotification("USB监听启动失败")
        }
    }
    
    /**
     * 注销USB广播接收器
     */
    private fun unregisterUsbReceiver() {
        try {
            if (isReceiverRegistered && usbBroadcastReceiver != null) {
                unregisterReceiver(usbBroadcastReceiver)
                isReceiverRegistered = false
                usbBroadcastReceiver = null
                
                Log.d(TAG, "USB广播接收器注销成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "注销USB广播接收器失败", e)
        }
    }
    
    /**
     * 启动健康检查
     */
    private fun startHealthCheck() {
        serviceScope.launch {
            while (isActive) {
                try {
                    delay(HEALTH_CHECK_INTERVAL)
                    performHealthCheck()
                } catch (e: Exception) {
                    Log.e(TAG, "健康检查失败", e)
                }
            }
        }
    }
    
    /**
     * 执行健康检查
     */
    private fun performHealthCheck() {
        try {
            val currentTime = System.currentTimeMillis()
            
            // 检查服务状态
            if (!isServiceHealthy) {
                Log.w(TAG, "服务状态不健康，尝试恢复")
                attemptServiceRecovery()
            }
            
            // 检查广播接收器状态
            if (!isReceiverRegistered) {
                Log.w(TAG, "广播接收器未注册，尝试重新注册")
                registerUsbReceiver()
            }
            
            // 更新健康检查时间
            lastHealthCheck = currentTime
            
            // 更新通知
            val status = if (isServiceHealthy && isReceiverRegistered) {
                "USB监听正常运行"
            } else {
                "USB监听状态异常"
            }
            updateNotification(status)
            
            Log.d(TAG, "健康检查完成: $status")
            
        } catch (e: Exception) {
            Log.e(TAG, "健康检查执行失败", e)
        }
    }
    
    /**
     * 尝试服务恢复
     */
    private fun attemptServiceRecovery() {
        try {
            Log.d(TAG, "开始服务恢复")
            
            // 重新注册广播接收器
            unregisterUsbReceiver()
            registerUsbReceiver()
            
            // 重置健康状态
            isServiceHealthy = true
            
            Log.d(TAG, "服务恢复完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "服务恢复失败", e)
            isServiceHealthy = false
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "USB监听服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "USB设备插拔监听服务"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "通知渠道已创建")
        }
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(content: String): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("儿童成长分析")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    /**
     * 更新通知
     */
    private fun updateNotification(content: String) {
        try {
            val notification = createNotification(content)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            Log.e(TAG, "更新通知失败", e)
        }
    }
    
    /**
     * 获取服务状态
     */
    fun getServiceStatus(): ServiceStatus {
        return ServiceStatus(
            isHealthy = isServiceHealthy,
            isReceiverRegistered = isReceiverRegistered,
            lastHealthCheck = lastHealthCheck,
            uptime = System.currentTimeMillis() - lastHealthCheck
        )
    }
}

/**
 * 服务状态数据类
 */
data class ServiceStatus(
    val isHealthy: Boolean,
    val isReceiverRegistered: Boolean,
    val lastHealthCheck: Long,
    val uptime: Long
)
