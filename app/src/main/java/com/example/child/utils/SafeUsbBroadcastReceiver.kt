package com.example.child.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.util.Log

/**
 * 安全的USB广播接收器
 * 不使用Hilt依赖注入，避免BroadcastReceiver相关的崩溃问题
 */
class SafeUsbBroadcastReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "SafeUsbBroadcastReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            Log.d(TAG, "收到USB广播: ${intent.action}")
            
            when (intent.action) {
                UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                    handleUsbDeviceAttached(context, intent)
                }
                UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                    handleUsbDeviceDetached(context, intent)
                }
                "android.intent.action.MEDIA_MOUNTED" -> {
                    handleMediaMounted(context, intent)
                }
                "android.intent.action.MEDIA_UNMOUNTED" -> {
                    handleMediaUnmounted(context, intent)
                }
                "android.intent.action.MEDIA_REMOVED" -> {
                    handleMediaRemoved(context, intent)
                }
                SafeUsbPermissionManager.ACTION_USB_PERMISSION -> {
                    handleUsbPermissionResult(context, intent)
                }
                else -> {
                    Log.d(TAG, "未处理的USB广播: ${intent.action}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理USB广播失败", e)
        }
    }
    
    /**
     * 处理USB设备插入
     */
    private fun handleUsbDeviceAttached(context: Context, intent: Intent) {
        try {
            val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
            device?.let {
                Log.d(TAG, "USB设备插入: ${it.deviceName}")
                
                // 启动USB处理服务
                startUsbHandlerService(context, UsbHandlerService.ACTION_DEVICE_ATTACHED, it)
                
                // 发送设备插入通知
                sendDeviceNotification(context, "USB设备已插入", it.deviceName)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理USB设备插入失败", e)
        }
    }
    
    /**
     * 处理USB设备拔出
     */
    private fun handleUsbDeviceDetached(context: Context, intent: Intent) {
        try {
            val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
            device?.let {
                Log.d(TAG, "USB设备拔出: ${it.deviceName}")
                
                // 启动USB处理服务
                startUsbHandlerService(context, UsbHandlerService.ACTION_DEVICE_DETACHED, it)
                
                // 发送设备拔出通知
                sendDeviceNotification(context, "USB设备已拔出", it.deviceName)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理USB设备拔出失败", e)
        }
    }
    
    /**
     * 处理媒体挂载
     */
    private fun handleMediaMounted(context: Context, intent: Intent) {
        try {
            val path = intent.data?.path
            Log.d(TAG, "媒体挂载: $path")
            
            // 启动媒体处理服务
            startMediaHandlerService(context, UsbHandlerService.ACTION_MEDIA_MOUNTED, path)
        } catch (e: Exception) {
            Log.e(TAG, "处理媒体挂载失败", e)
        }
    }
    
    /**
     * 处理媒体卸载
     */
    private fun handleMediaUnmounted(context: Context, intent: Intent) {
        try {
            val path = intent.data?.path
            Log.d(TAG, "媒体卸载: $path")
            
            // 启动媒体处理服务
            startMediaHandlerService(context, UsbHandlerService.ACTION_MEDIA_UNMOUNTED, path)
        } catch (e: Exception) {
            Log.e(TAG, "处理媒体卸载失败", e)
        }
    }
    
    /**
     * 处理媒体移除
     */
    private fun handleMediaRemoved(context: Context, intent: Intent) {
        try {
            val path = intent.data?.path
            Log.d(TAG, "媒体移除: $path")
            
            // 启动媒体处理服务
            startMediaHandlerService(context, UsbHandlerService.ACTION_MEDIA_REMOVED, path)
        } catch (e: Exception) {
            Log.e(TAG, "处理媒体移除失败", e)
        }
    }
    
    /**
     * 处理USB权限结果
     */
    private fun handleUsbPermissionResult(context: Context, intent: Intent) {
        try {
            val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
            val granted = intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)
            
            device?.let {
                Log.d(TAG, "USB权限结果: ${it.deviceName}, 授予: $granted")
                
                // 启动权限处理服务
                startPermissionHandlerService(context, it, granted)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理USB权限结果失败", e)
        }
    }
    
    /**
     * 启动USB处理服务
     */
    private fun startUsbHandlerService(context: Context, action: String, device: UsbDevice) {
        try {
            val serviceIntent = Intent(context, UsbHandlerService::class.java).apply {
                this.action = action
                putExtra(UsbHandlerService.EXTRA_USB_DEVICE, device)
            }
            context.startService(serviceIntent)
            Log.d(TAG, "USB处理服务已启动: $action")
        } catch (e: Exception) {
            Log.e(TAG, "启动USB处理服务失败", e)
        }
    }
    
    /**
     * 启动媒体处理服务
     */
    private fun startMediaHandlerService(context: Context, action: String, path: String?) {
        try {
            val serviceIntent = Intent(context, UsbHandlerService::class.java).apply {
                this.action = action
                putExtra(UsbHandlerService.EXTRA_MEDIA_PATH, path)
            }
            context.startService(serviceIntent)
            Log.d(TAG, "媒体处理服务已启动: $action")
        } catch (e: Exception) {
            Log.e(TAG, "启动媒体处理服务失败", e)
        }
    }
    
    /**
     * 启动权限处理服务
     */
    private fun startPermissionHandlerService(context: Context, device: UsbDevice, granted: Boolean) {
        try {
            val serviceIntent = Intent(context, UsbHandlerService::class.java).apply {
                action = UsbHandlerService.ACTION_PERMISSION_RESULT
                putExtra(UsbHandlerService.EXTRA_USB_DEVICE, device)
                putExtra(UsbHandlerService.EXTRA_PERMISSION_GRANTED, granted)
            }
            context.startService(serviceIntent)
            Log.d(TAG, "权限处理服务已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动权限处理服务失败", e)
        }
    }
    
    /**
     * 发送设备通知
     */
    private fun sendDeviceNotification(context: Context, title: String, deviceName: String) {
        try {
            // 这里可以发送系统通知
            // 暂时只记录日志
            Log.d(TAG, "设备通知: $title - $deviceName")
        } catch (e: Exception) {
            Log.e(TAG, "发送设备通知失败", e)
        }
    }
}
