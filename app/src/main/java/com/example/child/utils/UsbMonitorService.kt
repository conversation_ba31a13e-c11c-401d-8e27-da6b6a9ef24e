package com.example.child.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * USB监听后台服务
 * 持续监听USB设备状态，管理文件扫描任务
 */
@AndroidEntryPoint
class UsbMonitorService : Service() {

    @Inject
    lateinit var usbDeviceManager: UsbDeviceManager

    @Inject
    lateinit var fileWatcher: FileWatcher

    private lateinit var usbBroadcastReceiver: UsbBroadcastReceiver
    private var monitoringJob: Job? = null
    private var isServiceRunning = false

    companion object {
        private const val TAG = "UsbMonitorService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "usb_monitor_channel"
        private const val CHANNEL_NAME = "USB设备监听"
        private const val MONITORING_INTERVAL = 5000L // 5秒检查一次
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "USB监听服务创建")
        
        // 创建通知渠道
        createNotificationChannel()
        
        // 初始化广播接收器
        usbBroadcastReceiver = UsbBroadcastReceiver()
        
        // 注册广播接收器
        registerUsbBroadcastReceiver()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "USB监听服务启动")
        
        if (!isServiceRunning) {
            startForegroundService()
            startMonitoring()
            isServiceRunning = true
        }
        
        return START_STICKY // 服务被杀死后自动重启
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "USB监听服务销毁")
        
        stopMonitoring()
        unregisterUsbBroadcastReceiver()
        isServiceRunning = false
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null // 不支持绑定
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "监听USB设备插入状态"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 启动前台服务
     */
    private fun startForegroundService() {
        val notification = createNotification("正在监听USB设备...")
        startForeground(NOTIFICATION_ID, notification)
    }

    /**
     * 创建通知
     */
    private fun createNotification(content: String): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("USB设备监听")
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }

    /**
     * 更新通知内容
     */
    private fun updateNotification(content: String) {
        val notification = createNotification(content)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 注册USB广播接收器
     */
    private fun registerUsbBroadcastReceiver() {
        val filter = IntentFilter().apply {
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
            addAction(Intent.ACTION_MEDIA_MOUNTED)
            addAction(Intent.ACTION_MEDIA_UNMOUNTED)
            addAction(Intent.ACTION_MEDIA_REMOVED)
            addDataScheme("file")
        }
        
        registerReceiver(usbBroadcastReceiver, filter)
        Log.d(TAG, "USB广播接收器已注册")
    }

    /**
     * 注销USB广播接收器
     */
    private fun unregisterUsbBroadcastReceiver() {
        try {
            unregisterReceiver(usbBroadcastReceiver)
            Log.d(TAG, "USB广播接收器已注销")
        } catch (e: Exception) {
            Log.e(TAG, "注销广播接收器失败", e)
        }
    }

    /**
     * 开始监听
     */
    private fun startMonitoring() {
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            Log.d(TAG, "开始USB设备监听循环")
            
            while (isActive) {
                try {
                    // 定期扫描USB设备
                    scanUsbDevices()
                    
                    // 检查设备状态
                    checkDeviceStatus()
                    
                    // 等待下次检查
                    delay(MONITORING_INTERVAL)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "监听循环异常", e)
                    delay(MONITORING_INTERVAL)
                }
            }
        }
    }

    /**
     * 停止监听
     */
    private fun stopMonitoring() {
        monitoringJob?.cancel()
        fileWatcher.stopWatching()
        Log.d(TAG, "USB设备监听已停止")
    }

    /**
     * 扫描USB设备
     */
    private suspend fun scanUsbDevices() {
        try {
            usbDeviceManager.scanForUsbDevices()
            
            // 获取当前设备数量
            val deviceCount = usbDeviceManager.usbDevices.value.size
            
            if (deviceCount > 0) {
                updateNotification("已检测到 $deviceCount 个USB设备")
                
                // 开始文件监听
                usbDeviceManager.usbDevices.value.forEach { device ->
                    device.mountPath?.let { path ->
                        if (!fileWatcher.isWatching()) {
                            fileWatcher.startWatching(path)
                        }
                    }
                }
            } else {
                updateNotification("正在监听USB设备...")
                fileWatcher.stopWatching()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "扫描USB设备失败", e)
        }
    }

    /**
     * 检查设备状态
     */
    private fun checkDeviceStatus() {
        try {
            val usbManager = getSystemService(Context.USB_SERVICE) as UsbManager
            val deviceList = usbManager.deviceList
            
            Log.d(TAG, "当前连接的USB设备数量: ${deviceList.size}")
            
            deviceList.values.forEach { device ->
                Log.d(TAG, "USB设备: ${device.deviceName}, 类别: ${device.deviceClass}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查设备状态失败", e)
        }
    }

        /**
         * 静态方法：启动服务
         */
        fun startService(context: Context) {
            val intent = Intent(context, UsbMonitorService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        fun stopService(context: Context) {
            val intent = Intent(context, UsbMonitorService::class.java)
            context.stopService(intent)
        }
}
