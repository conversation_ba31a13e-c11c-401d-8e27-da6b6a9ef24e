package com.example.child.utils

import android.os.FileObserver
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 文件系统监听器
 * 监听USB设备中的文件变化，实时检测新文件添加
 */
@Singleton
class FileWatcher @Inject constructor(
    private val usbDeviceManager: UsbDeviceManager
) {
    
    companion object {
        private const val TAG = "FileWatcher"
    }

    private var fileObserver: FileObserver? = null
    private var isWatching = false
    
    // 新文件检测状态
    private val _newFilesDetected = MutableStateFlow<List<File>>(emptyList())
    val newFilesDetected: StateFlow<List<File>> = _newFilesDetected.asStateFlow()
    
    // 文件变化事件
    private val _fileChangeEvents = MutableStateFlow<FileChangeEvent?>(null)
    val fileChangeEvents: StateFlow<FileChangeEvent?> = _fileChangeEvents.asStateFlow()

    /**
     * 开始监听指定路径的文件变化
     */
    fun startWatching(path: String) {
        if (isWatching) {
            stopWatching()
        }
        
        val directory = File(path)
        if (!directory.exists() || !directory.isDirectory) {
            Log.w(TAG, "无效的监听路径: $path")
            return
        }
        
        Log.d(TAG, "开始监听文件变化: $path")
        
        fileObserver = object : FileObserver(path, ALL_EVENTS) {
            override fun onEvent(event: Int, path: String?) {
                handleFileEvent(event, path)
            }
        }
        
        fileObserver?.startWatching()
        isWatching = true
        
        // 初始扫描现有文件
        CoroutineScope(Dispatchers.IO).launch {
            scanExistingFiles(directory)
        }
    }

    /**
     * 停止文件监听
     */
    fun stopWatching() {
        if (isWatching) {
            Log.d(TAG, "停止文件监听")
            fileObserver?.stopWatching()
            fileObserver = null
            isWatching = false
        }
    }

    /**
     * 处理文件系统事件
     */
    private fun handleFileEvent(event: Int, path: String?) {
        if (path == null) return
        
        val eventType = when (event and FileObserver.ALL_EVENTS) {
            FileObserver.CREATE -> "CREATE"
            FileObserver.DELETE -> "DELETE"
            FileObserver.MODIFY -> "MODIFY"
            FileObserver.MOVED_FROM -> "MOVED_FROM"
            FileObserver.MOVED_TO -> "MOVED_TO"
            FileObserver.CLOSE_WRITE -> "CLOSE_WRITE"
            else -> "OTHER($event)"
        }
        
        Log.d(TAG, "文件事件: $eventType - $path")
        
        // 处理文件创建事件
        when (event and FileObserver.ALL_EVENTS) {
            FileObserver.CREATE,
            FileObserver.MOVED_TO,
            FileObserver.CLOSE_WRITE -> {
                handleFileCreated(path)
            }
            FileObserver.DELETE,
            FileObserver.MOVED_FROM -> {
                handleFileDeleted(path)
            }
        }
    }

    /**
     * 处理文件创建事件
     */
    private fun handleFileCreated(fileName: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 等待文件写入完成
                kotlinx.coroutines.delay(1000)
                
                // 重新扫描USB设备以获取最新文件列表
                usbDeviceManager.scanForUsbDevices()
                
                // 检查是否为音频文件
                if (isAudioFile(fileName)) {
                    Log.d(TAG, "检测到新的音频文件: $fileName")
                    
                    // 发送文件变化事件
                    _fileChangeEvents.value = FileChangeEvent(
                        type = FileChangeType.CREATED,
                        fileName = fileName,
                        timestamp = System.currentTimeMillis()
                    )
                    
                    // 触发新文件检测
                    detectNewAudioFiles()
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理文件创建事件失败", e)
            }
        }
    }

    /**
     * 处理文件删除事件
     */
    private fun handleFileDeleted(fileName: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 重新扫描USB设备
                usbDeviceManager.scanForUsbDevices()
                
                if (isAudioFile(fileName)) {
                    Log.d(TAG, "检测到音频文件删除: $fileName")
                    
                    // 发送文件变化事件
                    _fileChangeEvents.value = FileChangeEvent(
                        type = FileChangeType.DELETED,
                        fileName = fileName,
                        timestamp = System.currentTimeMillis()
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理文件删除事件失败", e)
            }
        }
    }

    /**
     * 扫描现有文件
     */
    private suspend fun scanExistingFiles(directory: File) {
        try {
            val audioFiles = mutableListOf<File>()
            findAudioFiles(directory, audioFiles)
            
            if (audioFiles.isNotEmpty()) {
                Log.d(TAG, "发现 ${audioFiles.size} 个音频文件")
                _newFilesDetected.value = audioFiles
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描现有文件失败", e)
        }
    }

    /**
     * 检测新的音频文件
     */
    private suspend fun detectNewAudioFiles() {
        try {
            val allAudioFiles = mutableListOf<File>()
            
            // 获取所有USB设备的音频文件
            usbDeviceManager.usbDevices.value.forEach { device ->
                val files = usbDeviceManager.getAudioFiles(device)
                allAudioFiles.addAll(files)
            }
            
            _newFilesDetected.value = allAudioFiles
        } catch (e: Exception) {
            Log.e(TAG, "检测新音频文件失败", e)
        }
    }

    /**
     * 递归查找音频文件
     */
    private fun findAudioFiles(directory: File, audioFiles: MutableList<File>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    findAudioFiles(file, audioFiles)
                } else if (file.isFile && isAudioFile(file.name)) {
                    audioFiles.add(file)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "查找音频文件失败: ${directory.path}", e)
        }
    }

    /**
     * 检查是否为音频文件
     */
    private fun isAudioFile(fileName: String): Boolean {
        val audioExtensions = listOf("mp3", "mp4", "wav", "m4a", "aac", "flac", "ogg", "3gp")
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return audioExtensions.contains(extension)
    }

    /**
     * 获取当前监听状态
     */
    fun isWatching(): Boolean = isWatching
}

/**
 * 文件变化事件数据类
 */
data class FileChangeEvent(
    val type: FileChangeType,
    val fileName: String,
    val timestamp: Long
)

/**
 * 文件变化类型枚举
 */
enum class FileChangeType {
    CREATED,
    DELETED,
    MODIFIED
}
