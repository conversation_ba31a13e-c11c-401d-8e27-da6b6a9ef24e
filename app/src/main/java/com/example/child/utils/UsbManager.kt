package com.example.child.utils

import android.content.Context
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

data class UsbDeviceInfo(
    val deviceName: String,
    val totalSpace: Long,
    val freeSpace: Long,
    val mountPath: String?,
    val isConnected: Boolean = true,
    val lastScanTime: Long = System.currentTimeMillis(),
    val audioFileCount: Int = 0
)

@Singleton
class UsbDeviceManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    private val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager

    private val _usbDevices = MutableStateFlow<List<UsbDeviceInfo>>(emptyList())
    val usbDevices: StateFlow<List<UsbDeviceInfo>> = _usbDevices.asStateFlow()

    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()

    // 新增：文件扫描状态
    private val _fileScanProgress = MutableStateFlow<FileScanProgress?>(null)
    val fileScanProgress: StateFlow<FileScanProgress?> = _fileScanProgress.asStateFlow()

    // 新增：自动扫描开关
    private val _autoScanEnabled = MutableStateFlow(true)
    val autoScanEnabled: StateFlow<Boolean> = _autoScanEnabled.asStateFlow()

    companion object {
        private const val TAG = "UsbDeviceManager"
    }
    
    fun scanForUsbDevices() {
        _isScanning.value = true
        Log.d(TAG, "开始扫描USB设备...")

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val devices = mutableListOf<UsbDeviceInfo>()

                // 获取USB设备
                val usbDevices = usbManager.deviceList
                Log.d(TAG, "发现 ${usbDevices.size} 个USB设备")

                for ((_, device) in usbDevices) {
                    if (isStorageDevice(device)) {
                        val deviceInfo = createUsbDeviceInfo(device)
                        deviceInfo?.let {
                            devices.add(it)
                            Log.d(TAG, "添加USB存储设备: ${it.deviceName}")
                        }
                    }
                }

                // 获取存储卷信息
                val storageVolumes = storageManager.storageVolumes
                Log.d(TAG, "发现 ${storageVolumes.size} 个存储卷")

                for (volume in storageVolumes) {
                    if (volume.isRemovable && volume.state == "mounted") {
                        val deviceInfo = createUsbDeviceInfoFromVolume(volume)
                        deviceInfo?.let {
                            devices.add(it)
                            Log.d(TAG, "添加存储卷设备: ${it.deviceName}")

                            // 自动开始文件扫描
                            if (_autoScanEnabled.value) {
                                startAutoFileScan(it)
                            }
                        }
                    }
                }

                _usbDevices.value = devices
                Log.d(TAG, "扫描完成，共发现 ${devices.size} 个可用设备")

            } catch (e: Exception) {
                Log.e(TAG, "扫描USB设备失败", e)
                _usbDevices.value = emptyList()
            } finally {
                _isScanning.value = false
            }
        }
    }
    
    private fun isStorageDevice(device: UsbDevice): Boolean {
        // 简单的存储设备检测逻辑
        return device.deviceClass == android.hardware.usb.UsbConstants.USB_CLASS_MASS_STORAGE ||
                device.deviceName.contains("storage", ignoreCase = true) ||
                device.deviceName.contains("disk", ignoreCase = true)
    }
    
    private fun createUsbDeviceInfo(device: UsbDevice): UsbDeviceInfo? {
        return try {
            UsbDeviceInfo(
                deviceName = device.deviceName,
                totalSpace = 0L, // 需要更复杂的逻辑来获取实际大小
                freeSpace = 0L,
                mountPath = null
            )
        } catch (e: Exception) {
            null
        }
    }
    
    private fun createUsbDeviceInfoFromVolume(volume: StorageVolume): UsbDeviceInfo? {
        return try {
            val path = getVolumePath(volume)
            val file = File(path)
            
            UsbDeviceInfo(
                deviceName = volume.getDescription(context) ?: "USB设备",
                totalSpace = file.totalSpace,
                freeSpace = file.freeSpace,
                mountPath = path
            )
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getVolumePath(volume: StorageVolume): String {
        return try {
            // 使用反射获取路径（Android 11+需要特殊处理）
            val getPathMethod = volume.javaClass.getMethod("getPath")
            getPathMethod.invoke(volume) as String
        } catch (e: Exception) {
            "/storage/unknown"
        }
    }
    
    fun clearUsbDevice(deviceInfo: UsbDeviceInfo): Boolean {
        return try {
            deviceInfo.mountPath?.let { path ->
                val directory = File(path)
                if (directory.exists() && directory.isDirectory) {
                    deleteRecursively(directory)
                    true
                } else {
                    false
                }
            } ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    private fun deleteRecursively(file: File): Boolean {
        return try {
            if (file.isDirectory) {
                file.listFiles()?.forEach { child ->
                    deleteRecursively(child)
                }
            }
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
    
    fun getAudioFiles(deviceInfo: UsbDeviceInfo): List<File> {
        val audioFiles = mutableListOf<File>()

        deviceInfo.mountPath?.let { path ->
            val directory = File(path)
            if (directory.exists() && directory.isDirectory) {
                findAudioFiles(directory, audioFiles)
            }
        }

        return audioFiles
    }

    private fun findAudioFiles(directory: File, audioFiles: MutableList<File>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    // 递归搜索子目录
                    findAudioFiles(file, audioFiles)
                } else if (file.isFile && isAudioFile(file)) {
                    audioFiles.add(file)
                }
            }
        } catch (e: Exception) {
            // 忽略权限错误等异常
        }
    }

    private fun isAudioFile(file: File): Boolean {
        val audioExtensions = listOf("mp3", "mp4", "wav", "m4a", "aac", "flac", "ogg", "3gp")
        val extension = file.extension.lowercase()
        return audioExtensions.contains(extension)
    }

    /**
     * 自动开始文件扫描
     */
    private fun startAutoFileScan(deviceInfo: UsbDeviceInfo) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始自动扫描设备文件: ${deviceInfo.deviceName}")

                _fileScanProgress.value = FileScanProgress(
                    deviceName = deviceInfo.deviceName,
                    isScanning = true,
                    currentPath = deviceInfo.mountPath ?: "",
                    filesFound = 0,
                    totalFiles = 0
                )

                deviceInfo.mountPath?.let { path ->
                    val directory = File(path)
                    if (directory.exists() && directory.isDirectory) {
                        val audioFiles = mutableListOf<File>()
                        scanDirectoryWithProgress(directory, audioFiles, deviceInfo.deviceName)

                        Log.d(TAG, "扫描完成，发现 ${audioFiles.size} 个音频文件")

                        _fileScanProgress.value = FileScanProgress(
                            deviceName = deviceInfo.deviceName,
                            isScanning = false,
                            currentPath = path,
                            filesFound = audioFiles.size,
                            totalFiles = audioFiles.size,
                            isCompleted = true
                        )
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "自动文件扫描失败", e)
                _fileScanProgress.value = FileScanProgress(
                    deviceName = deviceInfo.deviceName,
                    isScanning = false,
                    currentPath = "",
                    filesFound = 0,
                    totalFiles = 0,
                    error = e.message
                )
            }
        }
    }

    /**
     * 带进度的目录扫描
     */
    private suspend fun scanDirectoryWithProgress(
        directory: File,
        audioFiles: MutableList<File>,
        deviceName: String
    ) {
        try {
            directory.listFiles()?.forEach { file ->
                // 更新扫描进度
                _fileScanProgress.value = _fileScanProgress.value?.copy(
                    currentPath = file.absolutePath,
                    filesFound = audioFiles.size
                )

                if (file.isDirectory) {
                    // 递归扫描子目录
                    scanDirectoryWithProgress(file, audioFiles, deviceName)
                } else if (file.isFile && isAudioFile(file)) {
                    audioFiles.add(file)
                    Log.d(TAG, "发现音频文件: ${file.name}")
                }

                // 添加小延迟避免阻塞UI
                kotlinx.coroutines.delay(10)
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描目录失败: ${directory.path}", e)
        }
    }

    /**
     * 设置自动扫描开关
     */
    fun setAutoScanEnabled(enabled: Boolean) {
        _autoScanEnabled.value = enabled
        Log.d(TAG, "自动扫描${if (enabled) "已启用" else "已禁用"}")
    }

    /**
     * 手动触发文件扫描
     */
    fun triggerFileScan(deviceInfo: UsbDeviceInfo) {
        startAutoFileScan(deviceInfo)
    }

    /**
     * 获取设备连接状态
     */
    fun isDeviceConnected(deviceInfo: UsbDeviceInfo): Boolean {
        return deviceInfo.mountPath?.let { path ->
            File(path).exists()
        } ?: false
    }

    /**
     * 清除扫描进度
     */
    fun clearScanProgress() {
        _fileScanProgress.value = null
    }
}

/**
 * 文件扫描进度数据类
 */
data class FileScanProgress(
    val deviceName: String,
    val isScanning: Boolean,
    val currentPath: String,
    val filesFound: Int,
    val totalFiles: Int,
    val isCompleted: Boolean = false,
    val error: String? = null
)
