package com.example.child.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.DocumentsContract
import androidx.documentfile.provider.DocumentFile
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 基于Storage Access Framework的安全文件访问管理器
 * 用于Android 11+的安全文件访问
 */
@Singleton
class SafFileAccessManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "SafFileAccessManager"
        const val REQUEST_CODE_OPEN_DIRECTORY = 1001
        
        // 支持的音频文件格式
        private val AUDIO_EXTENSIONS = setOf(
            "mp3", "mp4", "wav", "m4a", "aac", "flac", "ogg", "3gp", "amr", "wma"
        )
    }
    
    // 文件访问状态
    private val _accessState = MutableStateFlow<FileAccessState>(FileAccessState.Idle)
    val accessState: StateFlow<FileAccessState> = _accessState.asStateFlow()
    
    // 扫描进度
    private val _scanProgress = MutableStateFlow<SafFileScanProgress?>(null)
    val scanProgress: StateFlow<SafFileScanProgress?> = _scanProgress.asStateFlow()
    
    // 发现的音频文件
    private val _audioFiles = MutableStateFlow<List<SafAudioFile>>(emptyList())
    val audioFiles: StateFlow<List<SafAudioFile>> = _audioFiles.asStateFlow()
    
    /**
     * 创建目录选择Intent
     */
    fun createDirectoryPickerIntent(): Intent {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
        return intent
    }
    
    /**
     * 处理目录选择结果
     */
    suspend fun handleDirectorySelection(uri: Uri): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                _accessState.value = FileAccessState.Processing("正在验证目录访问权限...")
                
                // 获取持久化权限
                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                context.contentResolver.takePersistableUriPermission(uri, takeFlags)
                
                // 验证目录访问
                val documentFile = DocumentFile.fromTreeUri(context, uri)
                if (documentFile == null || !documentFile.exists() || !documentFile.isDirectory) {
                    _accessState.value = FileAccessState.Error("无效的目录选择")
                    return@withContext false
                }
                
                _accessState.value = FileAccessState.Success("目录访问权限已获取", uri)
                Log.d(TAG, "目录访问权限获取成功: $uri")
                return@withContext true
                
            } catch (e: Exception) {
                Log.e(TAG, "处理目录选择失败", e)
                _accessState.value = FileAccessState.Error("目录访问失败: ${e.message}")
                return@withContext false
            }
        }
    }
    
    /**
     * 扫描目录中的音频文件
     */
    suspend fun scanAudioFiles(directoryUri: Uri): List<SafAudioFile> {
        return withContext(Dispatchers.IO) {
            try {
                _scanProgress.value = SafFileScanProgress(
                    isScanning = true,
                    currentPath = "开始扫描...",
                    filesFound = 0,
                    totalDirectories = 0,
                    scannedDirectories = 0
                )
                
                val audioFiles = mutableListOf<SafAudioFile>()
                val documentFile = DocumentFile.fromTreeUri(context, directoryUri)
                
                if (documentFile != null && documentFile.exists()) {
                    scanDirectoryRecursive(documentFile, audioFiles, "")
                }
                
                _audioFiles.value = audioFiles
                _scanProgress.value = SafFileScanProgress(
                    isScanning = false,
                    currentPath = "扫描完成",
                    filesFound = audioFiles.size,
                    totalDirectories = 0,
                    scannedDirectories = 0
                )
                
                Log.d(TAG, "音频文件扫描完成，发现 ${audioFiles.size} 个文件")
                return@withContext audioFiles
                
            } catch (e: Exception) {
                Log.e(TAG, "扫描音频文件失败", e)
                _scanProgress.value = SafFileScanProgress(
                    isScanning = false,
                    currentPath = "扫描失败",
                    filesFound = 0,
                    error = e.message
                )
                return@withContext emptyList()
            }
        }
    }
    
    /**
     * 递归扫描目录
     */
    private suspend fun scanDirectoryRecursive(
        directory: DocumentFile,
        audioFiles: MutableList<SafAudioFile>,
        relativePath: String
    ) {
        try {
            val currentPath = if (relativePath.isEmpty()) directory.name ?: "根目录" else relativePath
            
            _scanProgress.value = _scanProgress.value?.copy(
                currentPath = "扫描: $currentPath",
                scannedDirectories = _scanProgress.value!!.scannedDirectories + 1
            )
            
            directory.listFiles().forEach { file ->
                if (file.isDirectory) {
                    // 递归扫描子目录
                    val newPath = if (relativePath.isEmpty()) {
                        file.name ?: "未知目录"
                    } else {
                        "$relativePath/${file.name}"
                    }
                    scanDirectoryRecursive(file, audioFiles, newPath)
                } else if (file.isFile && isAudioFile(file.name)) {
                    // 添加音频文件
                    val audioFile = SafAudioFile(
                        uri = file.uri,
                        name = file.name ?: "未知文件",
                        path = relativePath,
                        size = file.length(),
                        lastModified = file.lastModified(),
                        mimeType = file.type
                    )
                    audioFiles.add(audioFile)
                    
                    _scanProgress.value = _scanProgress.value?.copy(
                        filesFound = audioFiles.size
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "扫描目录失败: $relativePath", e)
        }
    }
    
    /**
     * 判断是否为音频文件
     */
    private fun isAudioFile(fileName: String?): Boolean {
        if (fileName == null) return false
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return AUDIO_EXTENSIONS.contains(extension)
    }
    
    /**
     * 获取已保存的目录权限
     */
    fun getPersistedDirectoryUris(): List<Uri> {
        return try {
            context.contentResolver.persistedUriPermissions
                .filter { it.isReadPermission }
                .map { it.uri }
        } catch (e: Exception) {
            Log.e(TAG, "获取持久化权限失败", e)
            emptyList()
        }
    }
    
    /**
     * 释放目录权限
     */
    fun releaseDirectoryPermission(uri: Uri) {
        try {
            context.contentResolver.releasePersistableUriPermission(
                uri, Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            Log.d(TAG, "已释放目录权限: $uri")
        } catch (e: Exception) {
            Log.e(TAG, "释放目录权限失败", e)
        }
    }
    
    /**
     * 清除扫描结果
     */
    fun clearScanResults() {
        _audioFiles.value = emptyList()
        _scanProgress.value = null
        _accessState.value = FileAccessState.Idle
    }
    
    /**
     * 获取文件访问状态描述
     */
    fun getAccessStateDescription(state: FileAccessState): String {
        return when (state) {
            is FileAccessState.Idle -> "等待选择目录"
            is FileAccessState.Processing -> state.message
            is FileAccessState.Success -> state.message
            is FileAccessState.Error -> state.message
        }
    }
}

/**
 * 文件访问状态
 */
sealed class FileAccessState {
    object Idle : FileAccessState()
    data class Processing(val message: String) : FileAccessState()
    data class Success(val message: String, val uri: Uri) : FileAccessState()
    data class Error(val message: String) : FileAccessState()
}

/**
 * SAF文件扫描进度
 */
data class SafFileScanProgress(
    val isScanning: Boolean = false,
    val currentPath: String = "",
    val filesFound: Int = 0,
    val totalDirectories: Int = 0,
    val scannedDirectories: Int = 0,
    val error: String? = null
)

/**
 * SAF音频文件信息
 */
data class SafAudioFile(
    val uri: Uri,
    val name: String,
    val path: String,
    val size: Long,
    val lastModified: Long,
    val mimeType: String?
)
