package com.example.child

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.example.child.ui.navigation.AppNavigation
import com.example.child.ui.theme.ImmersiveChildGrowthTheme
import com.example.child.utils.UsbBroadcastReceiver
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private lateinit var usbManager: UsbManager
    private lateinit var usbBroadcastReceiver: UsbBroadcastReceiver

    companion object {
        private const val TAG = "MainActivity"
        private const val ACTION_USB_PERMISSION = "com.example.child.USB_PERMISSION"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 第六步：恢复USB管理器
        initializeUsbManager()
        handleUsbDeviceIntent(intent)

        setContent {
            ImmersiveChildGrowthTheme {
                // 移除 Surface，让内容直接填充整个屏幕
                AppNavigation()
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleUsbDeviceIntent(intent)
    }

    override fun onResume() {
        super.onResume()
        registerUsbReceiver()
    }

    override fun onPause() {
        super.onPause()
        unregisterUsbReceiver()
    }

    /**
     * 初始化USB管理器
     */
    private fun initializeUsbManager() {
        usbManager = getSystemService(Context.USB_SERVICE) as UsbManager
        usbBroadcastReceiver = UsbBroadcastReceiver()
    }

    /**
     * 处理USB设备插入Intent
     */
    private fun handleUsbDeviceIntent(intent: Intent) {
        when (intent.action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                device?.let {
                    Log.d(TAG, "USB设备插入: ${it.deviceName}")
                    requestUsbPermission(it)
                }
            }
        }
    }

    /**
     * 请求USB权限
     */
    private fun requestUsbPermission(device: UsbDevice) {
        if (!usbManager.hasPermission(device)) {
            Log.d(TAG, "请求USB权限: ${device.deviceName}")

            val permissionIntent = PendingIntent.getBroadcast(
                this,
                0,
                Intent(ACTION_USB_PERMISSION),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            usbManager.requestPermission(device, permissionIntent)
        } else {
            Log.d(TAG, "USB权限已存在: ${device.deviceName}")
        }
    }

    /**
     * 注册USB广播接收器
     */
    private fun registerUsbReceiver() {
        val filter = IntentFilter().apply {
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
            addAction(ACTION_USB_PERMISSION)
        }

        registerReceiver(usbBroadcastReceiver, filter)
        Log.d(TAG, "USB广播接收器已注册")
    }

    /**
     * 注销USB广播接收器
     */
    private fun unregisterUsbReceiver() {
        try {
            unregisterReceiver(usbBroadcastReceiver)
            Log.d(TAG, "USB广播接收器已注销")
        } catch (e: Exception) {
            Log.e(TAG, "注销USB广播接收器失败", e)
        }
    }
}