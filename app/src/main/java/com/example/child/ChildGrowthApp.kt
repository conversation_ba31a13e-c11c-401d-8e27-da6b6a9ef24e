package com.example.child

import android.app.Application
import android.content.Intent
import android.util.Log
import com.example.child.utils.UsbMonitorService
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class ChildGrowthApp : Application() {

    companion object {
        private const val TAG = "ChildGrowthApp"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "应用启动")

        // 第六步：启用自动启动USB服务
        try {
            initializeUsbMonitoring()
        } catch (e: Exception) {
            Log.e(TAG, "启动USB监听服务失败，但应用继续运行", e)
        }
    }

    /**
     * 初始化USB监听功能
     */
    private fun initializeUsbMonitoring() {
        try {
            Log.d(TAG, "初始化USB监听服务")
            val intent = Intent(this, UsbMonitorService::class.java)
            startService(intent)
        } catch (e: Exception) {
            Log.e(TAG, "启动USB监听服务失败", e)
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        Log.d(TAG, "应用终止")

        // 停止USB监听服务
        try {
            val intent = Intent(this, UsbMonitorService::class.java)
            stopService(intent)
        } catch (e: Exception) {
            Log.e(TAG, "停止USB监听服务失败", e)
        }
    }
}
