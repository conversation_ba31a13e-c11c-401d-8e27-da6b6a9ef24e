package com.example.child.ui.screens.device

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.R
import com.example.child.ui.components.RoundedCard

import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceManagementScreen(
    onBackClick: () -> Unit = {},
    viewModel: DeviceManagementViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    var showClearDialog by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.loadDeviceInfo()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Background)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            TopAppBar(
                title = {
                    Text(
                        text = "设备管理",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Primary
                )
            )
            // 可滚动内容
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    Spacer(modifier = Modifier.height(20.dp))

                    // USB兼容性检查卡片
                    UsbCompatibilityCard(
                        compatibilityResult = uiState.compatibilityResult,
                        showDetails = uiState.showCompatibilityInfo,
                        onToggleDetails = { viewModel.toggleCompatibilityInfo() },
                        onCheckCompatibility = { viewModel.checkUsbCompatibility() },
                        onRequestPermissions = { viewModel.requestUsbPermissions() }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(16.dp))

                    // SAF文件访问卡片
                    SafFileAccessCard(
                        fileAccessState = uiState.fileAccessState,
                        directorySelectionState = uiState.directorySelectionState,
                        audioFileCount = uiState.discoveredAudioFiles.size,
                        selectedDirectoryUri = uiState.selectedDirectoryUri,
                        showSafInterface = uiState.showSafInterface,
                        onToggleSafInterface = { viewModel.toggleSafInterface() },
                        onSelectDirectory = {
                            viewModel.requestDirectorySelection()
                        },
                        onClearResults = { viewModel.clearSafResults() }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(16.dp))

                    // 服务监控卡片
                    ServiceMonitorCard(
                        serviceState = uiState.serviceState,
                        serviceHealthStatus = uiState.serviceHealthStatus,
                        showServiceMonitor = uiState.showServiceMonitor,
                        onToggleServiceMonitor = { viewModel.toggleServiceMonitor() },
                        onStartService = { viewModel.startUsbMonitorService() },
                        onStopService = { viewModel.stopUsbMonitorService() },
                        onRestartService = { viewModel.restartUsbMonitorService() },
                        onCheckHealth = { viewModel.checkServiceHealth() }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(16.dp))

                    // USB监听状态卡片
                    UsbMonitoringCard(
                        isMonitoring = uiState.isUsbMonitoring,
                        deviceCount = uiState.usbDeviceCount,
                        audioFileCount = uiState.audioFileCount,
                        autoScanEnabled = uiState.autoScanEnabled,
                        onToggleAutoScan = { viewModel.toggleAutoScan() },
                        onManualScan = { viewModel.scanUsbDevices() }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(16.dp))

                    // 设备信息卡片
                    if (uiState.hasDevice) {
                        DeviceInfoCard(
                            deviceName = uiState.deviceName,
                            usedSpacePercentage = uiState.usedSpacePercentage,
                            totalSpace = uiState.totalSpace,
                            usedSpace = uiState.usedSpace
                        )
                    } else {
                        NoDeviceCard()
                    }
                }

                // 第四步：恢复文件扫描进度卡片
                uiState.fileScanProgress?.let { progress ->
                    item {
                        Spacer(modifier = Modifier.height(16.dp))
                        FileScanProgressCard(
                            progress = progress,
                            onClearProgress = { viewModel.clearScanProgress() }
                        )
                    }
                }

                if (uiState.hasDevice) {
                    item {
                        Spacer(modifier = Modifier.height(24.dp))

                        // 存储警告信息
                        if (uiState.usedSpacePercentage > 80) {
                            StorageWarningCard()
                        }
                    }

                    item {
                        Spacer(modifier = Modifier.height(32.dp))

                        // 清空设备数据按钮
                        ClearDeviceButton(
                            isLoading = uiState.isClearing,
                            onClick = { showClearDialog = true }
                        )
                    }
                }
            }
        }
    }

    // 清空设备确认对话框
    if (showClearDialog) {
        AlertDialog(
            onDismissRequest = { showClearDialog = false },
            title = {
                Text(text = stringResource(id = R.string.clear_device_data))
            },
            text = {
                Text(text = stringResource(id = R.string.clear_usb_confirm))
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.clearDevice()
                        showClearDialog = false
                    }
                ) {
                    Text(
                        text = stringResource(id = R.string.confirm),
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showClearDialog = false }
                ) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }

    // 显示操作结果
    LaunchedEffect(uiState.message) {
        if (uiState.message.isNotEmpty()) {
            // TODO: 显示 Snackbar 或 Toast
        }
    }
}

@Composable
private fun DeviceInfoCard(
    deviceName: String,
    usedSpacePercentage: Float,
    totalSpace: String,
    usedSpace: String
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 设备图标和名称
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    tint = Primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = deviceName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = TextPrimary
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // 圆形进度条
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.size(160.dp)
            ) {
                CircularProgressIndicator(
                    progress = { usedSpacePercentage / 100f },
                    modifier = Modifier.size(160.dp),
                    color = if (usedSpacePercentage > 80) MaterialTheme.colorScheme.error else Primary,
                    strokeWidth = 12.dp,
                    trackColor = Divider,
                    strokeCap = StrokeCap.Round
                )

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "${usedSpacePercentage.toInt()}%",
                        fontSize = 32.sp,
                        fontWeight = FontWeight.Bold,
                        color = TextPrimary
                    )
                    Text(
                        text = stringResource(id = R.string.used_space),
                        fontSize = 14.sp,
                        color = TextSecondary
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 存储空间信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "已用空间",
                        fontSize = 12.sp,
                        color = TextSecondary
                    )
                    Text(
                        text = usedSpace,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = TextPrimary
                    )
                }
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = "总空间",
                        fontSize = 12.sp,
                        color = TextSecondary
                    )
                    Text(
                        text = totalSpace,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = TextPrimary
                    )
                }
            }
        }
    }
}

@Composable
private fun NoDeviceCard() {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(40.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = null,
                tint = TextSecondary,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = stringResource(id = R.string.no_usb_device),
                fontSize = 16.sp,
                color = TextSecondary,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun StorageWarningCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f)
        )
    ) {
        Text(
            text = stringResource(id = R.string.storage_warning),
            modifier = Modifier.padding(16.dp),
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.error,
            lineHeight = 20.sp
        )
    }
}

@Composable
private fun ClearDeviceButton(
    isLoading: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = !isLoading,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.error,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
        } else {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = stringResource(id = R.string.clear_device_data),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun TopAppBarComponent(
    title: String,
    onBackClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Primary
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
    }
}

@Composable
private fun UsbMonitoringCard(
    isMonitoring: Boolean,
    deviceCount: Int,
    audioFileCount: Int,
    autoScanEnabled: Boolean,
    onToggleAutoScan: () -> Unit,
    onManualScan: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = null,
                        tint = if (isMonitoring) Primary else TextSecondary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "USB监听状态",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary
                    )
                }

                // 监听状态指示器
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            color = if (isMonitoring) Primary else TextSecondary,
                            shape = CircleShape
                        )
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 状态信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatusItem(
                    label = "设备数量",
                    value = "$deviceCount",
                    icon = Icons.Default.Settings
                )
                StatusItem(
                    label = "音频文件",
                    value = "$audioFileCount",
                    icon = Icons.Default.Settings
                )
            }

            Spacer(modifier = Modifier.height(20.dp))

            // 控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 自动扫描开关
                Button(
                    onClick = onToggleAutoScan,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (autoScanEnabled) Primary else TextSecondary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = if (autoScanEnabled) "自动扫描" else "手动模式",
                        fontSize = 14.sp
                    )
                }

                // 手动扫描按钮
                OutlinedButton(
                    onClick = onManualScan,
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "立即扫描",
                        fontSize = 14.sp,
                        color = Primary
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Primary,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = TextSecondary
        )
    }
}

@Composable
private fun FileScanProgressCard(
    progress: com.example.child.utils.FileScanProgress,
    onClearProgress: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = null,
                        tint = Primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "文件扫描",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary
                    )
                }

                if (progress.isCompleted) {
                    IconButton(onClick = onClearProgress) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = TextSecondary,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 设备名称
            Text(
                text = "设备: ${progress.deviceName}",
                fontSize = 14.sp,
                color = TextSecondary
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 扫描状态
            if (progress.isScanning) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Primary,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "正在扫描...",
                        fontSize = 14.sp,
                        color = Primary
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 当前路径
                Text(
                    text = "当前路径: ${progress.currentPath.takeLast(50)}",
                    fontSize = 12.sp,
                    color = TextSecondary,
                    maxLines = 1
                )
            } else if (progress.isCompleted) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = Primary,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "扫描完成",
                        fontSize = 14.sp,
                        color = Primary
                    )
                }
            } else if (progress.error != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "扫描失败: ${progress.error}",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 文件统计
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "发现文件: ${progress.filesFound}",
                    fontSize = 14.sp,
                    color = TextPrimary,
                    fontWeight = FontWeight.Medium
                )

                if (progress.totalFiles > 0) {
                    Text(
                        text = "总计: ${progress.totalFiles}",
                        fontSize = 14.sp,
                        color = TextSecondary
                    )
                }
            }
        }
    }
}

@Composable
private fun UsbCompatibilityCard(
    compatibilityResult: com.example.child.utils.UsbCompatibilityResult?,
    showDetails: Boolean,
    onToggleDetails: () -> Unit,
    onCheckCompatibility: () -> Unit,
    onRequestPermissions: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = null,
                        tint = Primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "USB兼容性检查",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary
                    )
                }

                // 兼容性评分
                compatibilityResult?.let { result ->
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .background(
                                color = when {
                                    result.compatibilityScore >= 80 -> Primary
                                    result.compatibilityScore >= 60 -> MaterialTheme.colorScheme.secondary
                                    else -> MaterialTheme.colorScheme.error
                                },
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "${result.compatibilityScore}",
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 兼容性状态
            compatibilityResult?.let { result ->
                Text(
                    text = when {
                        result.compatibilityScore >= 80 -> "✅ 完全兼容"
                        result.compatibilityScore >= 60 -> "⚠️ 基本兼容"
                        result.compatibilityScore >= 40 -> "⚠️ 部分兼容"
                        else -> "❌ 不兼容"
                    },
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = when {
                        result.compatibilityScore >= 80 -> Primary
                        result.compatibilityScore >= 40 -> MaterialTheme.colorScheme.secondary
                        else -> MaterialTheme.colorScheme.error
                    }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 详细信息
                if (showDetails) {
                    Column {
                        CompatibilityDetailItem("USB Host支持", result.hasUsbHostFeature)
                        CompatibilityDetailItem("API级别支持", result.apiLevelSupported)
                        CompatibilityDetailItem("USB管理器可用", result.usbManagerAvailable)
                        CompatibilityDetailItem("存储读取权限", result.storagePermissionStatus.hasReadExternalStorage)

                        if (result.recommendations.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(
                                text = "建议:",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = TextPrimary
                            )
                            result.recommendations.forEach { recommendation ->
                                Text(
                                    text = "• $recommendation",
                                    fontSize = 12.sp,
                                    color = TextSecondary,
                                    modifier = Modifier.padding(start = 8.dp, top = 4.dp)
                                )
                            }
                        }
                    }
                }
            } ?: run {
                Text(
                    text = "点击检查兼容性按钮开始检测",
                    fontSize = 14.sp,
                    color = TextSecondary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 检查兼容性按钮
                OutlinedButton(
                    onClick = onCheckCompatibility,
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "检查兼容性",
                        fontSize = 14.sp,
                        color = Primary
                    )
                }

                // 详情切换按钮
                if (compatibilityResult != null) {
                    Button(
                        onClick = onToggleDetails,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (showDetails) TextSecondary else Primary
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = if (showDetails) "隐藏详情" else "显示详情",
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun CompatibilityDetailItem(
    label: String,
    isSupported: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = TextSecondary
        )

        Icon(
            imageVector = if (isSupported) Icons.Default.CheckCircle else Icons.Default.Warning,
            contentDescription = null,
            tint = if (isSupported) Primary else MaterialTheme.colorScheme.error,
            modifier = Modifier.size(16.dp)
        )
    }
}

@Composable
private fun SafFileAccessCard(
    fileAccessState: com.example.child.utils.FileAccessState,
    directorySelectionState: com.example.child.utils.DirectorySelectionState,
    audioFileCount: Int,
    selectedDirectoryUri: android.net.Uri?,
    showSafInterface: Boolean,
    onToggleSafInterface: () -> Unit,
    onSelectDirectory: () -> Unit,
    onClearResults: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = null,
                        tint = Primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "SAF文件访问",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary
                    )
                }

                // 状态指示器
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            color = when (fileAccessState) {
                                is com.example.child.utils.FileAccessState.Success -> Primary
                                is com.example.child.utils.FileAccessState.Processing -> MaterialTheme.colorScheme.secondary
                                is com.example.child.utils.FileAccessState.Error -> MaterialTheme.colorScheme.error
                                else -> TextSecondary
                            },
                            shape = CircleShape
                        )
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 状态信息
            Text(
                text = when (fileAccessState) {
                    is com.example.child.utils.FileAccessState.Idle -> "等待选择目录"
                    is com.example.child.utils.FileAccessState.Processing -> fileAccessState.message
                    is com.example.child.utils.FileAccessState.Success -> fileAccessState.message
                    is com.example.child.utils.FileAccessState.Error -> fileAccessState.message
                },
                fontSize = 14.sp,
                color = when (fileAccessState) {
                    is com.example.child.utils.FileAccessState.Error -> MaterialTheme.colorScheme.error
                    else -> TextSecondary
                }
            )

            // 详细信息
            if (showSafInterface) {
                Spacer(modifier = Modifier.height(16.dp))

                // 选择的目录信息
                selectedDirectoryUri?.let { uri ->
                    SafDirectoryInfo(uri = uri, audioFileCount = audioFileCount)
                    Spacer(modifier = Modifier.height(12.dp))
                }

                // 目录选择状态
                SafDirectorySelectionStatus(state = directorySelectionState)
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 切换SAF界面
                Button(
                    onClick = onToggleSafInterface,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (showSafInterface) TextSecondary else Primary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = if (showSafInterface) "隐藏SAF" else "启用SAF",
                        fontSize = 14.sp
                    )
                }

                // 选择目录按钮
                if (showSafInterface) {
                    OutlinedButton(
                        onClick = onSelectDirectory,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = "选择目录",
                            fontSize = 14.sp,
                            color = Primary
                        )
                    }
                }

                // 清除结果按钮
                if (selectedDirectoryUri != null) {
                    OutlinedButton(
                        onClick = onClearResults,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = "清除",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SafDirectoryInfo(
    uri: android.net.Uri,
    audioFileCount: Int
) {
    Column {
        Text(
            text = "已选择目录:",
            fontSize = 12.sp,
            color = TextSecondary,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = uri.path?.substringAfterLast("/") ?: "未知目录",
            fontSize = 14.sp,
            color = TextPrimary,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = null,
                tint = Primary,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "音频文件: $audioFileCount",
                fontSize = 14.sp,
                color = TextPrimary,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun SafDirectorySelectionStatus(
    state: com.example.child.utils.DirectorySelectionState
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = when (state) {
                is com.example.child.utils.DirectorySelectionState.Ready -> Icons.Default.CheckCircle
                is com.example.child.utils.DirectorySelectionState.Selected -> Icons.Default.CheckCircle
                is com.example.child.utils.DirectorySelectionState.Error -> Icons.Default.Warning
                else -> Icons.Default.Info
            },
            contentDescription = null,
            tint = when (state) {
                is com.example.child.utils.DirectorySelectionState.Ready -> Primary
                is com.example.child.utils.DirectorySelectionState.Selected -> Primary
                is com.example.child.utils.DirectorySelectionState.Error -> MaterialTheme.colorScheme.error
                else -> TextSecondary
            },
            modifier = Modifier.size(16.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = when (state) {
                is com.example.child.utils.DirectorySelectionState.Idle -> "等待检测存储设备"
                is com.example.child.utils.DirectorySelectionState.Detecting -> state.message
                is com.example.child.utils.DirectorySelectionState.Ready -> state.message
                is com.example.child.utils.DirectorySelectionState.Validating -> state.message
                is com.example.child.utils.DirectorySelectionState.Selected -> state.message
                is com.example.child.utils.DirectorySelectionState.Error -> state.message
            },
            fontSize = 12.sp,
            color = when (state) {
                is com.example.child.utils.DirectorySelectionState.Error -> MaterialTheme.colorScheme.error
                else -> TextSecondary
            }
        )
    }
}

@Composable
private fun ServiceMonitorCard(
    serviceState: com.example.child.utils.UsbServiceState,
    serviceHealthStatus: com.example.child.utils.ServiceHealthStatus?,
    showServiceMonitor: Boolean,
    onToggleServiceMonitor: () -> Unit,
    onStartService: () -> Unit,
    onStopService: () -> Unit,
    onRestartService: () -> Unit,
    onCheckHealth: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Settings,
                        contentDescription = null,
                        tint = Primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "服务监控",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = TextPrimary
                    )
                }

                // 服务状态指示器
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            color = when (serviceState) {
                                is com.example.child.utils.UsbServiceState.Running -> Primary
                                is com.example.child.utils.UsbServiceState.Starting -> MaterialTheme.colorScheme.secondary
                                is com.example.child.utils.UsbServiceState.Stopping -> MaterialTheme.colorScheme.secondary
                                is com.example.child.utils.UsbServiceState.Error -> MaterialTheme.colorScheme.error
                                else -> TextSecondary
                            },
                            shape = CircleShape
                        )
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 服务状态
            Text(
                text = when (serviceState) {
                    is com.example.child.utils.UsbServiceState.Stopped -> "⏹️ 服务已停止"
                    is com.example.child.utils.UsbServiceState.Starting -> "🔄 服务启动中..."
                    is com.example.child.utils.UsbServiceState.Running -> "✅ 服务运行中"
                    is com.example.child.utils.UsbServiceState.Stopping -> "🔄 服务停止中..."
                    is com.example.child.utils.UsbServiceState.Error -> "❌ 服务错误: ${serviceState.message}"
                },
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = when (serviceState) {
                    is com.example.child.utils.UsbServiceState.Running -> Primary
                    is com.example.child.utils.UsbServiceState.Error -> MaterialTheme.colorScheme.error
                    else -> TextSecondary
                }
            )

            // 详细信息
            if (showServiceMonitor) {
                Spacer(modifier = Modifier.height(16.dp))

                serviceHealthStatus?.let { health ->
                    ServiceHealthInfo(healthStatus = health)
                    Spacer(modifier = Modifier.height(12.dp))
                }

                ServiceControlButtons(
                    serviceState = serviceState,
                    onStartService = onStartService,
                    onStopService = onStopService,
                    onRestartService = onRestartService,
                    onCheckHealth = onCheckHealth
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 切换监控界面
                Button(
                    onClick = onToggleServiceMonitor,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (showServiceMonitor) TextSecondary else Primary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = if (showServiceMonitor) "隐藏监控" else "显示监控",
                        fontSize = 14.sp
                    )
                }

                // 快速操作按钮
                if (!showServiceMonitor) {
                    when (serviceState) {
                        is com.example.child.utils.UsbServiceState.Stopped -> {
                            OutlinedButton(
                                onClick = onStartService,
                                modifier = Modifier.weight(1f),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                Text(
                                    text = "启动服务",
                                    fontSize = 14.sp,
                                    color = Primary
                                )
                            }
                        }
                        is com.example.child.utils.UsbServiceState.Running -> {
                            OutlinedButton(
                                onClick = onStopService,
                                modifier = Modifier.weight(1f),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                Text(
                                    text = "停止服务",
                                    fontSize = 14.sp,
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                        }
                        else -> {
                            OutlinedButton(
                                onClick = onCheckHealth,
                                modifier = Modifier.weight(1f),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                Text(
                                    text = "检查状态",
                                    fontSize = 14.sp,
                                    color = Primary
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ServiceHealthInfo(
    healthStatus: com.example.child.utils.ServiceHealthStatus
) {
    Column {
        Text(
            text = "服务健康状态:",
            fontSize = 12.sp,
            color = TextSecondary,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "运行状态:",
                fontSize = 14.sp,
                color = TextSecondary
            )
            Text(
                text = if (healthStatus.isRunning) "运行中" else "已停止",
                fontSize = 14.sp,
                color = if (healthStatus.isRunning) Primary else MaterialTheme.colorScheme.error,
                fontWeight = FontWeight.Medium
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "健康状态:",
                fontSize = 14.sp,
                color = TextSecondary
            )
            Text(
                text = if (healthStatus.isHealthy) "正常" else "异常",
                fontSize = 14.sp,
                color = if (healthStatus.isHealthy) Primary else MaterialTheme.colorScheme.error,
                fontWeight = FontWeight.Medium
            )
        }

        if (healthStatus.uptime > 0) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "运行时间:",
                    fontSize = 14.sp,
                    color = TextSecondary
                )
                Text(
                    text = formatUptime(healthStatus.uptime),
                    fontSize = 14.sp,
                    color = TextPrimary,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        healthStatus.error?.let { error ->
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "错误: $error",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

@Composable
private fun ServiceControlButtons(
    serviceState: com.example.child.utils.UsbServiceState,
    onStartService: () -> Unit,
    onStopService: () -> Unit,
    onRestartService: () -> Unit,
    onCheckHealth: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 启动按钮
        OutlinedButton(
            onClick = onStartService,
            modifier = Modifier.weight(1f),
            enabled = serviceState is com.example.child.utils.UsbServiceState.Stopped,
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "启动",
                fontSize = 12.sp,
                color = if (serviceState is com.example.child.utils.UsbServiceState.Stopped) Primary else TextSecondary
            )
        }

        // 停止按钮
        OutlinedButton(
            onClick = onStopService,
            modifier = Modifier.weight(1f),
            enabled = serviceState is com.example.child.utils.UsbServiceState.Running,
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "停止",
                fontSize = 12.sp,
                color = if (serviceState is com.example.child.utils.UsbServiceState.Running) MaterialTheme.colorScheme.error else TextSecondary
            )
        }

        // 重启按钮
        OutlinedButton(
            onClick = onRestartService,
            modifier = Modifier.weight(1f),
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "重启",
                fontSize = 12.sp,
                color = Primary
            )
        }

        // 检查健康按钮
        OutlinedButton(
            onClick = onCheckHealth,
            modifier = Modifier.weight(1f),
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "检查",
                fontSize = 12.sp,
                color = Primary
            )
        }
    }
}

private fun formatUptime(uptime: Long): String {
    val seconds = uptime / 1000
    val minutes = seconds / 60
    val hours = minutes / 60

    return when {
        hours > 0 -> "${hours}小时${minutes % 60}分钟"
        minutes > 0 -> "${minutes}分钟${seconds % 60}秒"
        else -> "${seconds}秒"
    }
}