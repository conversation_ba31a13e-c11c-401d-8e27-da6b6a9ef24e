package com.example.child.ui.screens.device

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.DeviceRepository
import com.example.child.utils.UsbDeviceManager
import com.example.child.utils.FileWatcher
import com.example.child.utils.FileScanProgress
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class DeviceManagementUiState(
    val isLoading: Boolean = false,
    val isClearing: Boolean = false,
    val hasDevice: Boolean = false,
    val deviceName: String = "",
    val totalSpace: String = "",
    val usedSpace: String = "",
    val usedSpacePercentage: Float = 0f,
    val message: String = "",
    val error: String = "",
    // 新增USB监听相关状态
    val isUsbMonitoring: Boolean = false,
    val usbDeviceCount: Int = 0,
    val fileScanProgress: FileScanProgress? = null,
    val audioFileCount: Int = 0,
    val autoScanEnabled: Boolean = true
)

@HiltViewModel
class DeviceManagementViewModel @Inject constructor(
    private val deviceRepository: DeviceRepository,
    private val usbDeviceManager: UsbDeviceManager,
    private val fileWatcher: FileWatcher
) : ViewModel() {

    private val _uiState = MutableStateFlow(DeviceManagementUiState())
    val uiState: StateFlow<DeviceManagementUiState> = _uiState.asStateFlow()

    init {
        // 极简版本：只设置静态UI状态，不执行任何USB操作
        _uiState.value = _uiState.value.copy(
            isUsbMonitoring = false,
            usbDeviceCount = 0,
            audioFileCount = 0,
            autoScanEnabled = true,
            message = "USB功能已禁用（极简模式）"
        )
    }
    
    fun loadDeviceInfo() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                val deviceInfo = deviceRepository.getConnectedDeviceInfo()
                if (deviceInfo != null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = true,
                        deviceName = deviceInfo.name,
                        totalSpace = formatBytes(deviceInfo.totalSpace),
                        usedSpace = formatBytes(deviceInfo.usedSpace),
                        usedSpacePercentage = (deviceInfo.usedSpace.toFloat() / deviceInfo.totalSpace.toFloat() * 100)
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasDevice = false,
                    error = e.message ?: "加载设备信息失败"
                )
            }
        }
    }
    
    fun clearDevice() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isClearing = true, message = "")
            
            try {
                val success = deviceRepository.clearDevice()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        message = "设备数据已清空",
                        usedSpace = "0 B",
                        usedSpacePercentage = 0f
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        error = "清空设备失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isClearing = false,
                    error = e.message ?: "清空设备失败"
                )
            }
        }
    }
    
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.1f %s", size, units[unitIndex])
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = "", error = "")
    }

    /**
     * 监听USB设备状态变化
     */
    private fun observeUsbDevices() {
        viewModelScope.launch {
            try {
                usbDeviceManager.usbDevices.collect { devices ->
                    val audioFileCount = try {
                        devices.sumOf { device ->
                            usbDeviceManager.getAudioFiles(device).size
                        }
                    } catch (e: Exception) {
                        0 // 如果获取音频文件失败，返回0
                    }

                    _uiState.value = _uiState.value.copy(
                        usbDeviceCount = devices.size,
                        audioFileCount = audioFileCount,
                        isUsbMonitoring = devices.isNotEmpty()
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "USB设备监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听文件扫描进度
     */
    private fun observeFileScanProgress() {
        viewModelScope.launch {
            try {
                usbDeviceManager.fileScanProgress.collect { progress ->
                    _uiState.value = _uiState.value.copy(
                        fileScanProgress = progress
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "文件扫描监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听文件变化事件
     */
    private fun observeFileChanges() {
        viewModelScope.launch {
            try {
                fileWatcher.newFilesDetected.collect { files ->
                    if (files.isNotEmpty()) {
                        _uiState.value = _uiState.value.copy(
                            message = "检测到 ${files.size} 个新文件",
                            audioFileCount = files.size
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "文件变化监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 手动扫描USB设备（极简版本 - 只更新UI）
     */
    fun scanUsbDevices() {
        _uiState.value = _uiState.value.copy(
            message = "USB扫描功能已禁用（极简模式）",
            error = ""
        )
    }

    /**
     * 切换自动扫描开关（极简版本 - 只更新UI）
     */
    fun toggleAutoScan() {
        val newState = !_uiState.value.autoScanEnabled
        _uiState.value = _uiState.value.copy(
            autoScanEnabled = newState,
            message = if (newState) "自动扫描已启用（极简模式）" else "手动模式已启用（极简模式）"
        )
    }

    /**
     * 手动触发文件扫描（极简版本 - 只更新UI）
     */
    fun triggerFileScan() {
        _uiState.value = _uiState.value.copy(
            message = "文件扫描功能已禁用（极简模式）"
        )
    }

    /**
     * 清除扫描进度（极简版本 - 只更新UI）
     */
    fun clearScanProgress() {
        _uiState.value = _uiState.value.copy(
            fileScanProgress = null,
            message = "进度已清除（极简模式）"
        )
    }
}
