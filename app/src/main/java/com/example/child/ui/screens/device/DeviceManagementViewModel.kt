package com.example.child.ui.screens.device

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.DeviceRepository
import com.example.child.utils.UsbDeviceManager
import com.example.child.utils.FileWatcher
import com.example.child.utils.FileScanProgress
import com.example.child.utils.UsbCompatibilityChecker
import com.example.child.utils.UsbCompatibilityResult
import com.example.child.utils.SafeUsbPermissionManager
import com.example.child.utils.UsbPermissionState
import com.example.child.utils.SafFileAccessManager
import com.example.child.utils.FileAccessState
import com.example.child.utils.UsbDirectorySelector
import com.example.child.utils.DirectorySelectionState
import com.example.child.utils.SafAudioFile
import com.example.child.utils.SafActivityBridge
import com.example.child.utils.UsbServiceMonitor
import com.example.child.utils.UsbServiceState
import com.example.child.utils.ServiceHealthStatus
import android.net.Uri
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class DeviceManagementUiState(
    val isLoading: Boolean = false,
    val isClearing: Boolean = false,
    val hasDevice: Boolean = false,
    val deviceName: String = "",
    val totalSpace: String = "",
    val usedSpace: String = "",
    val usedSpacePercentage: Float = 0f,
    val message: String = "",
    val error: String = "",
    // USB监听相关状态
    val isUsbMonitoring: Boolean = false,
    val usbDeviceCount: Int = 0,
    val fileScanProgress: FileScanProgress? = null,
    val audioFileCount: Int = 0,
    val autoScanEnabled: Boolean = true,
    // 兼容性检查状态
    val compatibilityResult: UsbCompatibilityResult? = null,
    val permissionStatus: UsbPermissionState = UsbPermissionState.Idle,
    val showCompatibilityInfo: Boolean = false,
    // SAF文件访问状态
    val fileAccessState: FileAccessState = FileAccessState.Idle,
    val directorySelectionState: DirectorySelectionState = DirectorySelectionState.Idle,
    val discoveredAudioFiles: List<SafAudioFile> = emptyList(),
    val selectedDirectoryUri: Uri? = null,
    val showSafInterface: Boolean = false,
    // 服务监控状态
    val serviceState: UsbServiceState = UsbServiceState.Stopped,
    val serviceHealthStatus: ServiceHealthStatus? = null,
    val showServiceMonitor: Boolean = false
)

@HiltViewModel
class DeviceManagementViewModel @Inject constructor(
    private val deviceRepository: DeviceRepository,
    private val usbDeviceManager: UsbDeviceManager,
    private val fileWatcher: FileWatcher,
    private val compatibilityChecker: UsbCompatibilityChecker,
    private val permissionManager: SafeUsbPermissionManager,
    private val safFileAccessManager: SafFileAccessManager,
    private val usbDirectorySelector: UsbDirectorySelector,
    private val safActivityBridge: SafActivityBridge,
    private val usbServiceMonitor: UsbServiceMonitor
) : ViewModel() {

    private val _uiState = MutableStateFlow(DeviceManagementUiState())
    val uiState: StateFlow<DeviceManagementUiState> = _uiState.asStateFlow()

    init {
        // 阶段一：启用兼容性检查和权限管理
        initializeUsbFeatures()
    }

    /**
     * 初始化USB功能
     */
    private fun initializeUsbFeatures() {
        viewModelScope.launch {
            try {
                // 1. 初始化权限管理器
                permissionManager.initialize()

                // 2. 执行兼容性检查
                checkUsbCompatibility()

                // 3. 监听权限状态变化
                observePermissionStatus()

                // 4. 监听SAF文件访问状态
                observeFileAccessState()

                // 5. 监听目录选择状态
                observeDirectorySelectionState()

                // 6. 监听SAF桥接器的目录选择结果
                observeSafBridgeResults()

                // 7. 监听服务状态
                observeServiceState()

                // 8. 设置初始状态
                _uiState.value = _uiState.value.copy(
                    autoScanEnabled = true,
                    message = "正在检查USB兼容性..."
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "USB功能初始化失败: ${e.message}",
                    message = "已切换到安全模式"
                )
            }
        }
    }
    
    fun loadDeviceInfo() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                val deviceInfo = deviceRepository.getConnectedDeviceInfo()
                if (deviceInfo != null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = true,
                        deviceName = deviceInfo.name,
                        totalSpace = formatBytes(deviceInfo.totalSpace),
                        usedSpace = formatBytes(deviceInfo.usedSpace),
                        usedSpacePercentage = (deviceInfo.usedSpace.toFloat() / deviceInfo.totalSpace.toFloat() * 100)
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasDevice = false,
                    error = e.message ?: "加载设备信息失败"
                )
            }
        }
    }
    
    fun clearDevice() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isClearing = true, message = "")
            
            try {
                val success = deviceRepository.clearDevice()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        message = "设备数据已清空",
                        usedSpace = "0 B",
                        usedSpacePercentage = 0f
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        error = "清空设备失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isClearing = false,
                    error = e.message ?: "清空设备失败"
                )
            }
        }
    }
    
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.1f %s", size, units[unitIndex])
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = "", error = "")
    }

    /**
     * 监听USB设备状态变化
     */
    private fun observeUsbDevices() {
        viewModelScope.launch {
            try {
                usbDeviceManager.usbDevices.collect { devices ->
                    val audioFileCount = try {
                        devices.sumOf { device ->
                            usbDeviceManager.getAudioFiles(device).size
                        }
                    } catch (e: Exception) {
                        0 // 如果获取音频文件失败，返回0
                    }

                    _uiState.value = _uiState.value.copy(
                        usbDeviceCount = devices.size,
                        audioFileCount = audioFileCount,
                        isUsbMonitoring = devices.isNotEmpty()
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "USB设备监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听文件扫描进度
     */
    private fun observeFileScanProgress() {
        viewModelScope.launch {
            try {
                usbDeviceManager.fileScanProgress.collect { progress ->
                    _uiState.value = _uiState.value.copy(
                        fileScanProgress = progress
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "文件扫描监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听文件变化事件
     */
    private fun observeFileChanges() {
        viewModelScope.launch {
            try {
                fileWatcher.newFilesDetected.collect { files ->
                    if (files.isNotEmpty()) {
                        _uiState.value = _uiState.value.copy(
                            message = "检测到 ${files.size} 个新文件",
                            audioFileCount = files.size
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "文件变化监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 手动扫描USB设备（极简版本 - 只更新UI）
     */
    fun scanUsbDevices() {
        _uiState.value = _uiState.value.copy(
            message = "USB扫描功能已禁用（极简模式）",
            error = ""
        )
    }

    /**
     * 切换自动扫描开关（极简版本 - 只更新UI）
     */
    fun toggleAutoScan() {
        val newState = !_uiState.value.autoScanEnabled
        _uiState.value = _uiState.value.copy(
            autoScanEnabled = newState,
            message = if (newState) "自动扫描已启用（极简模式）" else "手动模式已启用（极简模式）"
        )
    }

    /**
     * 手动触发文件扫描（极简版本 - 只更新UI）
     */
    fun triggerFileScan() {
        _uiState.value = _uiState.value.copy(
            message = "文件扫描功能已禁用（极简模式）"
        )
    }

    /**
     * 清除扫描进度（极简版本 - 只更新UI）
     */
    fun clearScanProgress() {
        _uiState.value = _uiState.value.copy(
            fileScanProgress = null,
            message = "进度已清除"
        )
    }

    /**
     * 检查USB兼容性
     */
    fun checkUsbCompatibility() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    message = "正在检查USB兼容性...",
                    error = ""
                )

                val result = compatibilityChecker.checkUsbHostSupport()

                _uiState.value = _uiState.value.copy(
                    compatibilityResult = result,
                    message = compatibilityChecker.getCompatibilityDescription(result.compatibilityScore),
                    showCompatibilityInfo = true
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "兼容性检查失败: ${e.message}",
                    message = ""
                )
            }
        }
    }

    /**
     * 监听权限状态变化
     */
    private fun observePermissionStatus() {
        viewModelScope.launch {
            permissionManager.permissionStatus.collect { status ->
                _uiState.value = _uiState.value.copy(
                    permissionStatus = status,
                    message = permissionManager.getPermissionStatusDescription(status)
                )
            }
        }
    }

    /**
     * 切换兼容性信息显示
     */
    fun toggleCompatibilityInfo() {
        _uiState.value = _uiState.value.copy(
            showCompatibilityInfo = !_uiState.value.showCompatibilityInfo
        )
    }

    /**
     * 请求USB权限
     */
    fun requestUsbPermissions() {
        viewModelScope.launch {
            try {
                // 这里暂时只是模拟，实际的USB设备检测在后续阶段实现
                _uiState.value = _uiState.value.copy(
                    message = "USB权限功能将在后续阶段实现"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "权限请求失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听SAF文件访问状态
     */
    private fun observeFileAccessState() {
        viewModelScope.launch {
            safFileAccessManager.accessState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    fileAccessState = state,
                    message = safFileAccessManager.getAccessStateDescription(state)
                )
            }
        }
    }

    /**
     * 监听目录选择状态
     */
    private fun observeDirectorySelectionState() {
        viewModelScope.launch {
            usbDirectorySelector.selectionState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    directorySelectionState = state,
                    message = usbDirectorySelector.getSelectionStateDescription(state)
                )
            }
        }
    }

    /**
     * 监听SAF桥接器的目录选择结果
     */
    private fun observeSafBridgeResults() {
        viewModelScope.launch {
            safActivityBridge.directorySelectionResults.collect { uri ->
                handleDirectorySelection(uri)
            }
        }
    }

    /**
     * 监听服务状态
     */
    private fun observeServiceState() {
        viewModelScope.launch {
            usbServiceMonitor.serviceState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    serviceState = state,
                    message = usbServiceMonitor.getServiceStateDescription(state)
                )
            }
        }
    }

    /**
     * 启用SAF文件访问界面
     */
    fun enableSafInterface() {
        viewModelScope.launch {
            try {
                // 检测存储卷
                usbDirectorySelector.detectStorageVolumes()

                _uiState.value = _uiState.value.copy(
                    showSafInterface = true,
                    message = "SAF文件访问已启用"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "启用SAF失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 创建目录选择Intent
     */
    fun createDirectoryPickerIntent(): android.content.Intent {
        return safFileAccessManager.createDirectoryPickerIntent()
    }

    /**
     * 请求目录选择（通过SAF桥接器）
     */
    fun requestDirectorySelection() {
        viewModelScope.launch {
            try {
                if (!safActivityBridge.isActivityCallbackAvailable()) {
                    _uiState.value = _uiState.value.copy(
                        error = "Activity回调不可用，无法启动目录选择器"
                    )
                    return@launch
                }

                val intent = safFileAccessManager.createDirectoryPickerIntent()
                safActivityBridge.requestDirectoryPicker(intent)

                _uiState.value = _uiState.value.copy(
                    message = "正在启动目录选择器..."
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "启动目录选择器失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 处理目录选择结果
     */
    fun handleDirectorySelection(uri: Uri) {
        viewModelScope.launch {
            try {
                // 处理目录选择
                val success = safFileAccessManager.handleDirectorySelection(uri)

                if (success) {
                    // 验证是否为USB目录
                    val validation = usbDirectorySelector.validateUsbDirectory(uri)

                    _uiState.value = _uiState.value.copy(
                        selectedDirectoryUri = uri,
                        message = validation.message
                    )

                    // 开始扫描音频文件
                    scanAudioFilesWithSaf(uri)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "处理目录选择失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 使用SAF扫描音频文件
     */
    fun scanAudioFilesWithSaf(directoryUri: Uri) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    message = "正在扫描音频文件..."
                )

                val audioFiles = safFileAccessManager.scanAudioFiles(directoryUri)

                _uiState.value = _uiState.value.copy(
                    discoveredAudioFiles = audioFiles,
                    audioFileCount = audioFiles.size,
                    message = "发现 ${audioFiles.size} 个音频文件"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "扫描音频文件失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除SAF扫描结果
     */
    fun clearSafResults() {
        safFileAccessManager.clearScanResults()
        usbDirectorySelector.clearSelection()

        _uiState.value = _uiState.value.copy(
            discoveredAudioFiles = emptyList(),
            selectedDirectoryUri = null,
            showSafInterface = false,
            message = "已清除扫描结果"
        )
    }

    /**
     * 切换SAF界面显示
     */
    fun toggleSafInterface() {
        val newState = !_uiState.value.showSafInterface
        if (newState) {
            enableSafInterface()
        } else {
            _uiState.value = _uiState.value.copy(
                showSafInterface = false,
                message = "SAF界面已隐藏"
            )
        }
    }

    /**
     * 启动USB监听服务
     */
    fun startUsbMonitorService() {
        viewModelScope.launch {
            try {
                val success = usbServiceMonitor.startUsbMonitorService()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        message = "USB监听服务启动中..."
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        error = "启动USB监听服务失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "启动服务失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 停止USB监听服务
     */
    fun stopUsbMonitorService() {
        viewModelScope.launch {
            try {
                val success = usbServiceMonitor.stopUsbMonitorService()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        message = "USB监听服务停止中..."
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        error = "停止USB监听服务失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "停止服务失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 重启USB监听服务
     */
    fun restartUsbMonitorService() {
        viewModelScope.launch {
            try {
                val success = usbServiceMonitor.restartUsbMonitorService()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        message = "USB监听服务重启中..."
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        error = "重启USB监听服务失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "重启服务失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 检查服务健康状态
     */
    fun checkServiceHealth() {
        viewModelScope.launch {
            try {
                val healthStatus = usbServiceMonitor.checkServiceHealth()
                _uiState.value = _uiState.value.copy(
                    serviceHealthStatus = healthStatus,
                    message = if (healthStatus.isHealthy) "服务状态正常" else "服务状态异常"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "检查服务健康状态失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 切换服务监控界面显示
     */
    fun toggleServiceMonitor() {
        val newState = !_uiState.value.showServiceMonitor
        _uiState.value = _uiState.value.copy(
            showServiceMonitor = newState,
            message = if (newState) "服务监控已启用" else "服务监控已隐藏"
        )

        // 如果显示服务监控，立即检查服务状态
        if (newState) {
            checkServiceHealth()
        }
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        try {
            permissionManager.cleanup()
            safFileAccessManager.clearScanResults()
        } catch (e: Exception) {
            // 忽略清理错误
        }
    }
}
