package com.example.child.ui.screens.device

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.DeviceRepository
import com.example.child.utils.UsbDeviceManager
import com.example.child.utils.FileWatcher
import com.example.child.utils.FileScanProgress
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class DeviceManagementUiState(
    val isLoading: Boolean = false,
    val isClearing: Boolean = false,
    val hasDevice: Boolean = false,
    val deviceName: String = "",
    val totalSpace: String = "",
    val usedSpace: String = "",
    val usedSpacePercentage: Float = 0f,
    val message: String = "",
    val error: String = "",
    // 新增USB监听相关状态
    val isUsbMonitoring: Boolean = false,
    val usbDeviceCount: Int = 0,
    val fileScanProgress: FileScanProgress? = null,
    val audioFileCount: Int = 0,
    val autoScanEnabled: Boolean = true
)

@HiltViewModel
class DeviceManagementViewModel @Inject constructor(
    private val deviceRepository: DeviceRepository,
    private val usbDeviceManager: UsbDeviceManager,
    private val fileWatcher: FileWatcher
) : ViewModel() {

    private val _uiState = MutableStateFlow(DeviceManagementUiState())
    val uiState: StateFlow<DeviceManagementUiState> = _uiState.asStateFlow()

    init {
        // 第一步：只恢复基础USB设备监听
        try {
            // 监听USB设备状态变化
            observeUsbDevices()

            // 第三步：恢复文件扫描进度监听
            observeFileScanProgress()

            // 第五步：恢复文件变化监听
            observeFileChanges()
        } catch (e: Exception) {
            // 如果初始化失败，记录错误但不崩溃
            _uiState.value = _uiState.value.copy(
                error = "USB监听初始化失败: ${e.message}"
            )
        }
    }
    
    fun loadDeviceInfo() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                val deviceInfo = deviceRepository.getConnectedDeviceInfo()
                if (deviceInfo != null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = true,
                        deviceName = deviceInfo.name,
                        totalSpace = formatBytes(deviceInfo.totalSpace),
                        usedSpace = formatBytes(deviceInfo.usedSpace),
                        usedSpacePercentage = (deviceInfo.usedSpace.toFloat() / deviceInfo.totalSpace.toFloat() * 100)
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasDevice = false,
                    error = e.message ?: "加载设备信息失败"
                )
            }
        }
    }
    
    fun clearDevice() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isClearing = true, message = "")
            
            try {
                val success = deviceRepository.clearDevice()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        message = "设备数据已清空",
                        usedSpace = "0 B",
                        usedSpacePercentage = 0f
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        error = "清空设备失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isClearing = false,
                    error = e.message ?: "清空设备失败"
                )
            }
        }
    }
    
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.1f %s", size, units[unitIndex])
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = "", error = "")
    }

    /**
     * 监听USB设备状态变化
     */
    private fun observeUsbDevices() {
        viewModelScope.launch {
            try {
                usbDeviceManager.usbDevices.collect { devices ->
                    val audioFileCount = try {
                        devices.sumOf { device ->
                            usbDeviceManager.getAudioFiles(device).size
                        }
                    } catch (e: Exception) {
                        0 // 如果获取音频文件失败，返回0
                    }

                    _uiState.value = _uiState.value.copy(
                        usbDeviceCount = devices.size,
                        audioFileCount = audioFileCount,
                        isUsbMonitoring = devices.isNotEmpty()
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "USB设备监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听文件扫描进度
     */
    private fun observeFileScanProgress() {
        viewModelScope.launch {
            try {
                usbDeviceManager.fileScanProgress.collect { progress ->
                    _uiState.value = _uiState.value.copy(
                        fileScanProgress = progress
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "文件扫描监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 监听文件变化事件
     */
    private fun observeFileChanges() {
        viewModelScope.launch {
            try {
                fileWatcher.newFilesDetected.collect { files ->
                    if (files.isNotEmpty()) {
                        _uiState.value = _uiState.value.copy(
                            message = "检测到 ${files.size} 个新文件",
                            audioFileCount = files.size
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "文件变化监听失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 手动扫描USB设备
     */
    fun scanUsbDevices() {
        try {
            _uiState.value = _uiState.value.copy(
                message = "正在扫描USB设备...",
                error = ""
            )
            usbDeviceManager.scanForUsbDevices()
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "扫描USB设备失败: ${e.message}",
                message = ""
            )
        }
    }

    /**
     * 切换自动扫描开关
     */
    fun toggleAutoScan() {
        val newState = !_uiState.value.autoScanEnabled
        usbDeviceManager.setAutoScanEnabled(newState)
        _uiState.value = _uiState.value.copy(autoScanEnabled = newState)
    }

    /**
     * 手动触发文件扫描
     */
    fun triggerFileScan() {
        usbDeviceManager.usbDevices.value.firstOrNull()?.let { device ->
            usbDeviceManager.triggerFileScan(device)
        }
    }

    /**
     * 清除扫描进度
     */
    fun clearScanProgress() {
        usbDeviceManager.clearScanProgress()
    }
}
