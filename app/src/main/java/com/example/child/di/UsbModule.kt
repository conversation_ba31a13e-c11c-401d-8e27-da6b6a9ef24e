package com.example.child.di

import android.content.Context
import com.example.child.utils.FileWatcher
import com.example.child.utils.UsbDeviceManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * USB相关依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object UsbModule {

    @Provides
    @Singleton
    fun provideFileWatcher(
        usbDeviceManager: UsbDeviceManager
    ): FileWatcher {
        return FileWatcher(usbDeviceManager)
    }
}
